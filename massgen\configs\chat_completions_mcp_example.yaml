# Chat Completions Backend with MCP Integration Example Configuration


agents:
  - id: "chat_completions_mcp_test"
    backend:
      # Backend configuration
      type: "chatcompletion"
      provider: "OpenAI"  # or "Cerebras AI", "Together AI", "Fireworks AI", etc.
      base_url: "https://api.openai.com/v1"  # Optional, defaults to OpenAI
      
      # MCP Server Configuration
      # Only stdio and streamable-HTTP transport types are supported
      mcp_servers:
        # Simple test server for validating MCP integration
        - name: "test_server"
          transport_type: "stdio"
          command: "python"
          args: ["-u", "-m", "massgen.tests.mcp_test_server"]
          description: "Test MCP server with basic tools for validation"
      
      # MCP Tool Filtering Configuration
      # Control which MCP tools are available to the model
      
      # Option 1: Allow only specific tools (whitelist)
      allowed_tools:
        - "mcp_echo"
        - "add_numbers"
      
      # Option 2: Exclude specific tools (blacklist)
      # exclude_tools:
      #   - "delete_file"
      #   - "git_push"
      #   - "system_command"
      
      # Provider-specific tool configuration
      enable_web_search: false  # Use MCP search tools instead
      enable_code_interpreter: true
    
    # System message for the agent
    system_message: |
      You are an AI assistant with access to MCP tools through Chat Completions API.
      
      You have access to the following MCP tools:
      - mcp_echo: Echo back input text
      - add_numbers: Add two numbers together
      - get_current_time: Get the current timestamp
      
      Use these tools to demonstrate MCP integration functionality.

# UI Configuration
ui:
  display_type: "rich_terminal"
  logging_enabled: true
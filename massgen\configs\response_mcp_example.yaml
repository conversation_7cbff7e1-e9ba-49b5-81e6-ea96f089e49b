# MassGen Configuration: OpenAI Response API with MCP Integration
# Usage:
#   uv run python -m massgen.cli --config configs/response_mcp_example.yaml "Test the MCP tools by calling mcp_echo with text 'Hello massgen' and add_numbers with 46 and 52"

agents:
  - id: "response_mcp_test"
    backend:
      type: "openai"
      model: "gpt-5-nano"
      # MCP servers configuration for ResponseBackend
      mcp_servers:
        # Stdio MCP server example
        - name: "test_server"
          type: "stdio"
          command: "python"
          args: ["-u", "-m", "massgen.tests.mcp_test_server"]
      
    system_message: |
      You are an AI assistant with access to MCP tools through OpenAI's Response API.
      
      You have access to:
      - Stdio MCP tools from test_server (echo, add_numbers, get_current_time)
      
      Use these tools as needed to answer user questions comprehensively.

ui:
  display_type: "rich_terminal"
  logging_enabled: true

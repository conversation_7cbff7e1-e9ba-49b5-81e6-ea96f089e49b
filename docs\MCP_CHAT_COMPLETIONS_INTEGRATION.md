# MCP Integration in Chat Completions Backend

## Overview

The ChatCompletionsBackend now supports Model Context Protocol (MCP) integration, enabling AI models to access external tools and data sources through local execution. This integration provides a powerful way to extend model capabilities while maintaining security and control.

### Key Differences from Response API

Unlike the Response API which has native MCP support, Chat Completions API requires local execution of MCP tools:

- **Local Execution Only**: All MCP tools are executed locally and results passed to the Chat Completions API
- **Supported Transport Types**: Only `stdio` and `streamable-http` (local HTTP servers)
- **Not Supported**: Native HTTP MCP servers (external APIs)
- **Tool Format**: MCP tools are converted to Chat Completions function format

## Supported Transport Types

### 1. Stdio Transport

Process-based MCP servers spawned via command/args configuration.

```yaml
mcp_servers:
  - name: "filesystem_tools"
    transport_type: "stdio"
    command: "python"
    args: ["-m", "mcp_servers.filesystem"]
```

**Characteristics:**
- Local process execution
- Secure and isolated
- Direct stdin/stdout communication
- Ideal for file operations, git, local tools

### 2. Streamable-HTTP Transport

Local HTTP MCP servers with streaming execution under local control.

```yaml
mcp_servers:
  - name: "local_database"
    transport_type: "streamable-http"
    url: "http://localhost:8080/mcp"
```

**Characteristics:**
- Local HTTP communication only
- URL validation enforces localhost/127.0.0.1
- Streaming support for large responses
- Ideal for databases, local APIs, web services

### 3. HTTP Transport (Not Supported)

Native HTTP MCP servers are not supported in Chat Completions integration.

```yaml
# This will be skipped with a warning
mcp_servers:
  - name: "external_api"
    transport_type: "http"  # NOT SUPPORTED
    url: "https://external-api.com/mcp"
```

## Configuration Guide

### Basic Configuration

```yaml
backend: "chat_completions"
provider: "OpenAI"
model: "gpt-4o-mini"

mcp_servers:
  - name: "file_tools"
    transport_type: "stdio"
    command: "python"
    args: ["-m", "mcp_servers.filesystem"]
    
  - name: "local_api"
    transport_type: "streamable-http"
    url: "http://localhost:3000/mcp"
```

### Tool Filtering

Control which MCP tools are available to the model:

```yaml
# Whitelist approach
allowed_tools:
  - "read_file"
  - "write_file"
  - "git_status"

# Blacklist approach
exclude_tools:
  - "delete_file"
  - "system_command"
```

### Security Configuration

```yaml
mcp_servers:
  - name: "secure_server"
    transport_type: "stdio"
    command: "python"
    args: ["-m", "secure_mcp_server"]
    env:
      API_KEY: "${SECURE_API_KEY}"  # Use environment variables
      
  - name: "local_db"
    transport_type: "streamable-http"
    url: "http://127.0.0.1:5432/mcp"  # Only local URLs allowed
    headers:
      Authorization: "Bearer ${DB_TOKEN}"
    timeout: 30
```

## Usage Examples

### Example 1: File Operations with Git

```python
from massgen.backend.chat_completions import ChatCompletionsBackend

backend = ChatCompletionsBackend(
    api_key="your-openai-key",
    mcp_servers=[
        {
            "name": "filesystem",
            "transport_type": "stdio",
            "command": "python",
            "args": ["-m", "mcp_servers.filesystem"]
        },
        {
            "name": "git",
            "transport_type": "stdio", 
            "command": "npx",
            "args": ["@modelcontextprotocol/server-git"]
        }
    ],
    allowed_tools=["read_file", "write_file", "git_status", "git_diff"]
)

messages = [{"role": "user", "content": "Read the README.md file and check git status"}]

async for chunk in backend.stream_with_tools(messages, []):
    if chunk.type == "content":
        print(chunk.content)
```

### Example 2: Database Integration

```python
backend = ChatCompletionsBackend(
    api_key="your-openai-key",
    mcp_servers=[
        {
            "name": "database",
            "transport_type": "streamable-http",
            "url": "http://localhost:5432/mcp",
            "headers": {"Authorization": "Bearer your-db-token"}
        }
    ]
)

messages = [{"role": "user", "content": "Query the users table for active users"}]

async for chunk in backend.stream_with_tools(messages, []):
    if chunk.type == "tool_calls":
        print(f"Executing tools: {[tc['function']['name'] for tc in chunk.tool_calls]}")
    elif chunk.type == "content":
        print(chunk.content)
```

## Tool Format Reference

### MCP Tool to Chat Completions Conversion

MCP tools are automatically converted to Chat Completions format:

**MCP Format:**
```json
{
  "name": "read_file",
  "description": "Read contents of a file",
  "inputSchema": {
    "type": "object",
    "properties": {
      "path": {"type": "string", "description": "File path"}
    },
    "required": ["path"]
  }
}
```

**Chat Completions Format:**
```json
{
  "type": "function",
  "function": {
    "name": "read_file",
    "description": "Read contents of a file",
    "parameters": {
      "type": "object",
      "properties": {
        "path": {"type": "string", "description": "File path"}
      },
      "required": ["path"]
    }
  }
}
```

### Tool Execution Flow

1. **Setup Phase**: MCP servers are connected and tools registered
2. **Streaming Phase**: Model receives all available tools (MCP + provider)
3. **Tool Calls**: Model requests tool execution via Chat Completions format
4. **MCP Execution**: MCP tools are executed locally via appropriate client
5. **Result Integration**: Tool results are formatted and sent back to model
6. **Iteration**: Process continues until completion or max iterations

### Tool Result Messages

```json
{
  "role": "tool",
  "tool_call_id": "call_123",
  "content": "File contents: Hello, World!"
}
```

## Error Handling

### Connection Failures

```python
# Automatic fallback to non-MCP tools
async for chunk in backend.stream_with_tools(messages, []):
    if chunk.type == "content" and "⚠️ MCP connection failed" in chunk.content:
        print("MCP unavailable, using provider tools only")
```

### Tool Execution Errors

```json
{
  "role": "tool",
  "tool_call_id": "call_456", 
  "content": "Error executing MCP tool 'read_file': File not found"
}
```

### Circuit Breaker Behavior

- **Failure Threshold**: 3 failures before circuit opens
- **Recovery Timeout**: 300 seconds before retry attempts
- **Progressive Backoff**: 1s, 2s, 4s retry delays
- **Server Isolation**: Failed servers are isolated, others continue working

## Performance Considerations

### Latency Impact

- MCP tool execution adds latency to responses
- Stdio tools: ~50-200ms overhead per call
- HTTP tools: ~100-500ms overhead per call
- Network latency for local HTTP servers

### Optimization Strategies

```yaml
# Optimize for performance
mcp_servers:
  - name: "fast_tools"
    transport_type: "stdio"  # Generally faster than HTTP
    command: "optimized-mcp-server"
    
# Limit tool scope
allowed_tools: ["essential_tool_1", "essential_tool_2"]

# Set reasonable timeouts
timeout: 10  # seconds
```

### Concurrent Execution

- Multiple MCP tools can execute concurrently
- Circuit breakers prevent cascading failures
- Memory usage scales with number of active servers

## Debugging and Troubleshooting

### Enable Debug Logging

```python
import logging
logging.getLogger("massgen.backend.chat_completions").setLevel(logging.DEBUG)
```

### Common Issues

1. **MCP Server Not Starting**
   ```
   ERROR: Failed to connect to stdio MCP servers after all retries
   ```
   - Check command/args configuration
   - Verify MCP server installation
   - Check environment variables

2. **HTTP Server Unreachable**
   ```
   WARNING: Invalid or non-local URL for streamable-HTTP server
   ```
   - Ensure URL uses localhost/127.0.0.1
   - Verify HTTP server is running
   - Check port availability

3. **Tool Not Found**
   ```
   Error executing MCP tool 'missing_tool': Tool missing_tool not found
   ```
   - Verify tool name in allowed_tools
   - Check MCP server tool registration
   - Review exclude_tools configuration

### Statistics and Monitoring

```python
# Access MCP statistics
backend = ChatCompletionsBackend(...)
print(f"Tool calls: {backend._mcp_stats['tool_calls']}")
print(f"Failures: {backend._mcp_stats['failures']}")
print(f"Stdio failures: {backend._mcp_stats['stdio_failures']}")
print(f"HTTP failures: {backend._mcp_stats['streamable_http_failures']}")
```

## Limitations

### Compared to Response API

1. **No Native MCP Support**: Tools executed locally, not by provider
2. **Limited Transport Types**: Only stdio and streamable-HTTP
3. **No External HTTP**: Cannot connect to external MCP servers
4. **Tool Result Size**: Limited by Chat Completions message size limits
5. **Iteration Limit**: Maximum 10 tool execution iterations per conversation

### Security Limitations

1. **Local Execution Only**: Cannot access external MCP services
2. **Command Validation**: Limited validation of stdio commands
3. **URL Restrictions**: HTTP servers must be local
4. **Environment Variables**: Sensitive data exposure risk

### Performance Limitations

1. **Sequential Processing**: Tool calls processed in sequence
2. **Memory Usage**: Scales with number of servers and tools
3. **Connection Overhead**: Each server maintains persistent connection
4. **Timeout Constraints**: Long-running tools may timeout

## Best Practices

### Security

1. **Use Environment Variables**: Store sensitive data in environment variables
2. **Validate Commands**: Carefully validate stdio command configurations
3. **Local URLs Only**: Restrict HTTP servers to localhost
4. **Tool Filtering**: Use allowed_tools to limit available functionality
5. **Regular Updates**: Keep MCP servers and dependencies updated

### Performance

1. **Minimize Tool Count**: Only include necessary tools
2. **Optimize Servers**: Use efficient MCP server implementations
3. **Set Timeouts**: Configure appropriate timeout values
4. **Monitor Statistics**: Track tool usage and failure rates
5. **Circuit Breakers**: Rely on built-in circuit breaker protection

### Reliability

1. **Error Handling**: Implement proper error handling in applications
2. **Fallback Strategies**: Design for MCP unavailability
3. **Health Checks**: Monitor MCP server health
4. **Graceful Degradation**: Continue operation without MCP tools
5. **Cleanup**: Properly disconnect clients when done

## Migration from Response API

If migrating from Response API MCP integration:

1. **Remove HTTP Servers**: Only stdio and streamable-HTTP supported
2. **Update Configuration**: Use Chat Completions backend format
3. **Tool Format**: Tools automatically converted to Chat Completions format
4. **Error Handling**: Update error handling for local execution model
5. **Testing**: Thoroughly test tool execution and error scenarios

## Future Enhancements

Planned improvements for Chat Completions MCP integration:

1. **Parallel Tool Execution**: Execute multiple tools concurrently
2. **Enhanced Security**: Improved command and URL validation
3. **Performance Monitoring**: Built-in performance metrics
4. **Tool Caching**: Cache tool results for repeated calls
5. **Advanced Filtering**: More sophisticated tool filtering options

"""
Base class for backends using OpenAI Chat Completions API format.
Handles common message processing, tool conversion, and streaming patterns.

Supported Providers and Environment Variables:
- OpenAI: OPENAI_API_KEY
- Cerebras AI: CEREBRAS_API_KEY
- Together AI: TOGETHER_API_KEY
- Fireworks AI: FIREWORKS_API_KEY
- Groq: GROQ_API_KEY
- Nebius AI Studio: NEBIUS_API_KEY
- OpenRouter: OPENROUTER_API_KEY
- ZAI: ZAI_API_KEY
"""

from __future__ import annotations
# Standard library imports
import asyncio
import os
from dataclasses import dataclass
from typing import Dict, List, Any, AsyncGenerator, Optional, Tuple
from urllib.parse import urlparse

# Third-party imports
import openai
from openai import AsyncOpenAI
import logging

# Local imports
from .base import LLMBackend, StreamChunk

# MCP imports (conditional)
try:
    from ..mcp_tools.client import MultiMCPClient
    from ..mcp_tools.exceptions import (
        MCPError,
        MCPConnectionError,
        MCPConfigurationError,
        MCPValidationError,
        MCPTimeoutError,
        MCPServerError,
    )
    from ..mcp_tools.config_validator import MCPConfigValidator
    from ..mcp_tools.circuit_breaker import MCPCircuitBreaker
    from ..mcp_tools.security import validate_url
except ImportError:
    # Fallback assignments if MCP is not available
    MultiMCPClient = None
    MCPError = Exception
    MCPConnectionError = Exception
    MCPConfigurationError = Exception
    MCPValidationError = Exception
    MCPTimeoutError = Exception
    MCPServerError = Exception
    MCPConfigValidator = None
    MCPCircuitBreaker = None
    validate_url = None

# Set up logger
logger = logging.getLogger(__name__)


@dataclass
class Function:
    """Wrapper for MCP tools to convert to Chat Completions format."""
    
    name: str
    description: str
    parameters: Dict[str, Any]
    
    def to_openai_format(self) -> Dict[str, Any]:
        """Convert to Chat Completions tool format."""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters
            }
        }


class ChatCompletionsBackend(LLMBackend):
    """Complete OpenAI-compatible Chat Completions API backend.

    Can be used directly with any OpenAI-compatible provider by setting provider name.
    Supports Cerebras AI, Together AI, Fireworks AI, DeepInfra, and other compatible providers.

    Environment Variables:
        Provider-specific API keys are automatically detected based on provider name.
        See ProviderRegistry.PROVIDERS for the complete list.

    """

    def __init__(self, api_key: Optional[str] = None, **kwargs):
        super().__init__(api_key, **kwargs)
        
        # MCP configuration parameters
        self.mcp_servers = kwargs.get('mcp_servers', [])
        self.allowed_tools = kwargs.get('allowed_tools', None)
        self.exclude_tools = kwargs.get('exclude_tools', None)
        
        # MCP-related instance variables
        self._mcp_client = None
        self._streamable_mcp_client = None
        self._mcp_initialized = False
        self._streamable_http_initialized = False
        
        # Circuit breakers for stdio and streamable-HTTP
        self._stdio_circuit_breaker = None
        self._streamable_http_circuit_breaker = None
        
        # Server separation lists (only stdio and streamable-HTTP)
        self._stdio_servers = []
        self._streamable_http_servers = []
        
        # Function registry for MCP tools
        self._mcp_functions = {}
        
        # Statistics tracking with async lock
        self._mcp_stats = {
            'tool_calls': 0,
            'failures': 0,
            'stdio_failures': 0,
            'streamable_http_failures': 0
        }
        self._stats_lock = None  # Will be initialized when needed

    def get_provider_name(self) -> str:
        """Get the name of this provider."""
        # Check if provider name was explicitly set in config
        if "provider" in self.config:
            return self.config["provider"]
        elif "provider_name" in self.config:
            return self.config["provider_name"]

        # Try to infer from base_url
        base_url = self.config.get("base_url", "")
        if "openai.com" in base_url:
            return "OpenAI"
        elif "cerebras.ai" in base_url:
            return "Cerebras AI"
        elif "together.ai" in base_url:
            return "Together AI"
        elif "fireworks.ai" in base_url:
            return "Fireworks AI"
        elif "groq.com" in base_url:
            return "Groq"
        elif "nebius.ai" in base_url:
            return "Nebius AI Studio"
        elif "openrouter.ai" in base_url:
            return "OpenRouter"
        elif "z.ai" in base_url:
            return "ZAI"
        else:
            return "ChatCompletion"
    
    def _normalize_mcp_servers(self, servers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Normalize MCP server configurations."""
        normalized = []
        for server in servers:
            if isinstance(server, dict):
                # Ensure required fields exist
                normalized_server = {
                    'name': server.get('name', f'server_{len(normalized)}'),
                    'type': server.get('type', 'stdio'),
                    **server
                }
                normalized.append(normalized_server)
        return normalized
    
    def _separate_mcp_servers_by_transport_type(self, servers: List[Dict[str, Any]]) -> None:
        """Separate MCP servers by transport type (only stdio and streamable-HTTP)."""
        self._stdio_servers = []
        self._streamable_http_servers = []
        
        for server in servers:
            transport_type = server.get('type', 'stdio')
            
            if transport_type == 'stdio':
                self._stdio_servers.append(server)
            elif transport_type == 'streamable-http':
                self._streamable_http_servers.append(server)
            # Skip 'http' type as Chat Completions API doesn't support native MCP
            elif transport_type == 'http':
                logger.warning(f"Skipping HTTP MCP server '{server.get('name', 'unknown')}' - not supported in Chat Completions API")
            else:
                logger.warning(f"Unknown transport type '{transport_type}' for server '{server.get('name', 'unknown')}'")
    
    def _get_mcp_error_info(self, error: Exception) -> Tuple[str, str]:
        """Get error type and message from MCP exception."""
        if isinstance(error, MCPConnectionError):
            return "connection", str(error)
        elif isinstance(error, MCPTimeoutError):
            return "timeout", str(error)
        elif isinstance(error, MCPConfigurationError):
            return "configuration", str(error)
        elif isinstance(error, MCPValidationError):
            return "validation", str(error)
        elif isinstance(error, MCPServerError):
            return "server", str(error)
        else:
            return "unknown", str(error)
    
    def _log_mcp_error(self, operation: str, error: Exception, server_name: str = None) -> None:
        """Log MCP error with context."""
        error_type, error_msg = self._get_mcp_error_info(error)
        server_info = f" for server '{server_name}'" if server_name else ""
        logger.error(f"MCP {operation} {error_type} error{server_info}: {error_msg}")

    def convert_tools_to_chat_completions_format(
        self, tools: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert tools from Response API format to Chat Completions format if needed.

        Response API format: {"type": "function", "name": ..., "description": ..., "parameters": ...}
        Chat Completions format: {"type": "function", "function": {"name": ..., "description": ..., "parameters": ...}}
        """
        if not tools:
            return tools

        converted_tools = []
        for tool in tools:
            if tool.get("type") == "function":
                if "function" in tool:
                    # Already in Chat Completions format
                    converted_tools.append(tool)
                elif "name" in tool and "description" in tool:
                    # Response API format - convert to Chat Completions format
                    converted_tools.append(
                        {
                            "type": "function",
                            "function": {
                                "name": tool["name"],
                                "description": tool["description"],
                                "parameters": tool.get("parameters", {}),
                            },
                        }
                    )
                else:
                    # Unknown format - keep as-is
                    converted_tools.append(tool)
            else:
                # Non-function tool - keep as-is
                converted_tools.append(tool)

        return converted_tools
    
    async def _setup_mcp_tools(self) -> None:
        """Setup stdio MCP tools with configuration validation and circuit breaker filtering."""
        if not MultiMCPClient or not self._stdio_servers or self._mcp_initialized:
            return
        
        try:
            # Initialize stats lock if needed
            if self._stats_lock is None:
                self._stats_lock = asyncio.Lock()
            
            # Initialize circuit breaker for stdio
            if self._stdio_circuit_breaker is None:
                from ..mcp_tools.circuit_breaker import CircuitBreakerConfig
                config = CircuitBreakerConfig(
                    max_failures=3,
                    reset_time_seconds=300
                )
                self._stdio_circuit_breaker = MCPCircuitBreaker(config)
            
            # Validate configuration
            validator = MCPConfigValidator()
            valid_servers = []
            
            for server in self._stdio_servers:
                try:
                    validator.validate_server_config(server)
                    server_name = server.get('name', 'unknown')
                    
                    # Check circuit breaker status
                    if not self._stdio_circuit_breaker.should_skip_server(server_name):
                        valid_servers.append(server)
                    else:
                        logger.warning(f"Skipping stdio MCP server '{server_name}' - circuit breaker open")
                        
                except (MCPConfigurationError, MCPValidationError) as e:
                    self._log_mcp_error("configuration validation", e, server.get('name'))
                    continue
            
            if not valid_servers:
                logger.info("No valid stdio MCP servers available")
                return
            
            # Create and connect MCP client with retry logic
            max_retries = 3
            base_delay = 1.0
            
            for attempt in range(max_retries):
                try:
                    # Create MultiMCP client with server configs
                    self._mcp_client = MultiMCPClient(
                        server_configs=valid_servers,
                        timeout_seconds=30,
                        allowed_tools=self.allowed_tools,
                        exclude_tools=self.exclude_tools,
                    )
                    
                    # Connect to all servers
                    await self._mcp_client.connect()
                    
                    # Register tools as Function objects in Chat Completions format
                    available_tool_names = self._mcp_client.get_available_tools()
                    
                    for tool_name in available_tool_names:
                        # Get tool object from MultiMCPClient
                        if tool_name in self._mcp_client.tools:
                            mcp_tool = self._mcp_client.tools[tool_name]
                            # Convert MCP tool to Function
                            function = Function(
                                name=tool_name,
                                description=getattr(mcp_tool, 'description', ''),
                                parameters=getattr(mcp_tool, 'inputSchema', {})
                            )
                            self._mcp_functions[tool_name] = function
                    
                    # Record successful connection
                    for server in valid_servers:
                        server_name = server.get('name', 'unknown')
                        self._stdio_circuit_breaker.record_success(server_name)
                    
                    self._mcp_initialized = True
                    logger.info(f"Successfully initialized stdio MCP with {len(self._mcp_functions)} tools")
                    break
                    
                except (MCPConnectionError, MCPTimeoutError) as e:
                    self._log_mcp_error("connection", e)
                    
                    # Record failures for circuit breaker
                    for server in valid_servers:
                        server_name = server.get('name', 'unknown')
                        self._stdio_circuit_breaker.record_failure(server_name)
                    
                    # Update failure statistics
                    async with self._stats_lock:
                        self._mcp_stats['stdio_failures'] += 1
                        self._mcp_stats['failures'] += 1
                    
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)
                        logger.info(f"Retrying stdio MCP connection in {delay}s (attempt {attempt + 1}/{max_retries})")
                        await asyncio.sleep(delay)
                    else:
                        logger.error("Failed to connect to stdio MCP servers after all retries")
                        raise
                        
                except MCPServerError as e:
                    self._log_mcp_error("server", e)
                    
                    # Record failures for circuit breaker
                    for server in valid_servers:
                        server_name = server.get('name', 'unknown')
                        self._stdio_circuit_breaker.record_failure(server_name)
                    
                    async with self._stats_lock:
                        self._mcp_stats['stdio_failures'] += 1
                        self._mcp_stats['failures'] += 1
                    
                    raise
                    
                except Exception as e:
                    self._log_mcp_error("unknown", e)
                    
                    async with self._stats_lock:
                        self._mcp_stats['stdio_failures'] += 1
                        self._mcp_stats['failures'] += 1
                    
                    raise
                    
        except Exception as e:
            logger.error(f"Failed to setup stdio MCP tools: {e}")
            self._mcp_client = None
            self._mcp_initialized = False
    
    async def _setup_local_http_mcp_tools(self) -> None:
        """Setup streamable-HTTP MCP tools with URL validation and circuit breaker filtering."""
        if not MultiMCPClient or not self._streamable_http_servers or self._streamable_http_initialized:
            return
        
        try:
            # Initialize stats lock if needed
            if self._stats_lock is None:
                self._stats_lock = asyncio.Lock()
            
            # Initialize circuit breaker for streamable-HTTP
            if self._streamable_http_circuit_breaker is None:
                from ..mcp_tools.circuit_breaker import CircuitBreakerConfig
                config = CircuitBreakerConfig(
                    max_failures=3,
                    reset_time_seconds=300
                )
                self._streamable_http_circuit_breaker = MCPCircuitBreaker(config)
            
            # Validate configuration and URLs
            validator = MCPConfigValidator()
            valid_servers = []
            
            for server in self._streamable_http_servers:
                try:
                    validator.validate_server_config(server)
                    server_name = server.get('name', 'unknown')
                    
                    # URL validation for streamable HTTP servers
                    url = server.get('url', '')
                    if validate_url is not None:
                        try:
                            validate_url(url, allow_localhost=True, allow_private_ips=True)
                        except ValueError as e:
                            logger.warning(f"Invalid URL for streamable-HTTP server '{server_name}': {e}")
                            if self._streamable_http_circuit_breaker:
                                self._streamable_http_circuit_breaker.record_failure(server_name)
                            continue
                    
                    # Check circuit breaker status
                    if not self._streamable_http_circuit_breaker.should_skip_server(server_name):
                        valid_servers.append(server)
                    else:
                        logger.warning(f"Skipping streamable-HTTP MCP server '{server_name}' - circuit breaker open")
                        
                except (MCPConfigurationError, MCPValidationError) as e:
                    self._log_mcp_error("configuration validation", e, server.get('name'))
                    continue
            
            if not valid_servers:
                logger.info("No valid streamable-HTTP MCP servers available")
                return
            
            # Create and connect streamable MCP client with retry logic
            max_retries = 3
            base_delay = 1.0
            
            for attempt in range(max_retries):
                try:
                    # Create MultiMCP client with server configs
                    self._streamable_mcp_client = MultiMCPClient(
                        server_configs=valid_servers,
                        timeout_seconds=30,
                        allowed_tools=self.allowed_tools,
                        exclude_tools=self.exclude_tools,
                    )
                    
                    # Connect to all servers
                    await self._streamable_mcp_client.connect()
                    
                    # Register tools as Function objects
                    available_tool_names = self._streamable_mcp_client.get_available_tools()
                    
                    for tool_name in available_tool_names:
                        # Avoid name conflicts with stdio tools
                        prefixed_tool_name = tool_name
                        if tool_name in self._mcp_functions:
                            prefixed_tool_name = f"http_{tool_name}"
                        
                        # Get tool object from MultiMCPClient
                        if tool_name in self._streamable_mcp_client.tools:
                            mcp_tool = self._streamable_mcp_client.tools[tool_name]
                            # Convert MCP tool to Function
                            function = Function(
                                name=prefixed_tool_name,
                                description=getattr(mcp_tool, 'description', ''),
                                parameters=getattr(mcp_tool, 'inputSchema', {})
                            )
                            self._mcp_functions[prefixed_tool_name] = function
                    
                    # Record successful connection
                    for server in valid_servers:
                        server_name = server.get('name', 'unknown')
                        self._streamable_http_circuit_breaker.record_success(server_name)
                    
                    self._streamable_http_initialized = True
                    local_tools_count = len([name for name in self._mcp_functions.keys() if name.startswith('http_')])
                    logger.info(f"Successfully initialized streamable-HTTP MCP with {local_tools_count} tools")
                    break
                    
                except (MCPConnectionError, MCPTimeoutError) as e:
                    self._log_mcp_error("connection", e)
                    
                    # Record failures for circuit breaker
                    for server in valid_servers:
                        server_name = server.get('name', 'unknown')
                        self._streamable_http_circuit_breaker.record_failure(server_name)
                    
                    # Update failure statistics
                    async with self._stats_lock:
                        self._mcp_stats['streamable_http_failures'] += 1
                        self._mcp_stats['failures'] += 1
                    
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)
                        logger.info(f"Retrying streamable-HTTP MCP connection in {delay}s (attempt {attempt + 1}/{max_retries})")
                        await asyncio.sleep(delay)
                    else:
                        logger.error("Failed to connect to streamable-HTTP MCP servers after all retries")
                        raise
                        
                except MCPServerError as e:
                    self._log_mcp_error("server", e)
                    
                    # Record failures for circuit breaker
                    for server in valid_servers:
                        server_name = server.get('name', 'unknown')
                        self._streamable_http_circuit_breaker.record_failure(server_name)
                    
                    async with self._stats_lock:
                        self._mcp_stats['streamable_http_failures'] += 1
                        self._mcp_stats['failures'] += 1
                    
                    raise
                    
                except Exception as e:
                    self._log_mcp_error("unknown", e)
                    
                    async with self._stats_lock:
                        self._mcp_stats['streamable_http_failures'] += 1
                        self._mcp_stats['failures'] += 1
                    
                    raise
                    
        except Exception as e:
            logger.error(f"Failed to setup streamable-HTTP MCP tools: {e}")
            self._streamable_mcp_client = None
            self._streamable_http_initialized = False
    
    def _convert_mcp_tools_to_chat_completions_format(self) -> List[Dict[str, Any]]:
        """Convert MCP tools to Chat Completions format."""
        mcp_tools = []
        
        # Convert stdio and streamable-HTTP MCP Functions to Chat Completions tool format
        for tool_name, function in self._mcp_functions.items():
            mcp_tools.append(function.to_openai_format())
        
        return mcp_tools
    
    async def _execute_mcp_tools(self, tool_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute MCP tools and return tool result messages."""
        tool_results = []
        
        for tool_call in tool_calls:
            tool_name = self.extract_tool_name(tool_call)
            tool_args = self.extract_tool_arguments(tool_call)
            tool_call_id = self.extract_tool_call_id(tool_call)
            
            # Check if this is an MCP tool
            if tool_name in self._mcp_functions:
                try:
                    # Update statistics
                    if self._stats_lock:
                        async with self._stats_lock:
                            self._mcp_stats['tool_calls'] += 1
                    
                    # Determine which client to use based on tool name
                    if tool_name.startswith('http_') and self._streamable_mcp_client:
                        # Use streamable-HTTP client
                        result = await self._streamable_mcp_client.call_tool(tool_name.replace('http_', ''), tool_args)
                    elif self._mcp_client:
                        # Use stdio client
                        result = await self._mcp_client.call_tool(tool_name, tool_args)
                    else:
                        result = {"error": "MCP client not available"}
                    
                    # Create tool result message
                    result_content = str(result) if result else "Tool executed successfully"
                    tool_result = self.create_tool_result_message(tool_call, result_content)
                    tool_results.append(tool_result)
                    
                except Exception as e:
                    self._log_mcp_error("tool execution", e, tool_name)
                    
                    # Update failure statistics
                    if self._stats_lock:
                        async with self._stats_lock:
                            self._mcp_stats['failures'] += 1
                    
                    # Create error result message
                    error_content = f"Error executing MCP tool '{tool_name}': {str(e)}"
                    tool_result = self.create_tool_result_message(tool_call, error_content)
                    tool_results.append(tool_result)
            
            # Non-MCP tools are handled by the provider (OpenAI, etc.)
            # We don't need to execute them here
        
        return tool_results

    async def handle_chat_completions_stream(
        self, stream, enable_web_search: bool = False
    ) -> AsyncGenerator[StreamChunk, None]:
        """Handle standard Chat Completions API streaming format."""
        import json

        content = ""
        current_tool_calls = {}
        search_sources_used = 0

        async for chunk in stream:
            try:
                if hasattr(chunk, "choices") and chunk.choices:
                    choice = chunk.choices[0]

                    # Handle content delta
                    if hasattr(choice, "delta") and choice.delta:
                        delta = choice.delta

                        # Plain text content
                        if getattr(delta, "content", None):
                            # handle reasoning first
                            reasoning_active_key = f"_reasoning_active"
                            if hasattr(self, reasoning_active_key):
                                if getattr(self, reasoning_active_key) == True:
                                    setattr(self, reasoning_active_key, False)
                                    yield StreamChunk(type="reasoning_done", content="")
                            content_chunk = delta.content
                            content += content_chunk
                            yield StreamChunk(type="content", content=content_chunk)

                        # Provider-specific reasoning/thinking streams (non-standard OpenAI fields)
                        if getattr(delta, "reasoning_content", None):
                            reasoning_active_key = f"_reasoning_active"
                            setattr(self, reasoning_active_key, True)
                            thinking_delta = getattr(delta, "reasoning_content")
                            if thinking_delta:
                                yield StreamChunk(
                                    type="reasoning",
                                    content=thinking_delta,
                                    reasoning_delta=thinking_delta,
                                )

                        # Tool calls streaming (OpenAI-style)
                        if getattr(delta, "tool_calls", None):
                            # handle reasoning first
                            reasoning_active_key = f"_reasoning_active"
                            if hasattr(self, reasoning_active_key):
                                if getattr(self, reasoning_active_key) == True:
                                    setattr(self, reasoning_active_key, False)
                                    yield StreamChunk(type="reasoning_done", content="")

                            for tool_call_delta in delta.tool_calls:
                                index = getattr(tool_call_delta, "index", 0)

                                if index not in current_tool_calls:
                                    current_tool_calls[index] = {
                                        "id": "",
                                        "function": {
                                            "name": "",
                                            "arguments": "",
                                        },
                                    }

                                # Accumulate id
                                if getattr(tool_call_delta, "id", None):
                                    current_tool_calls[index]["id"] = tool_call_delta.id

                                # Function name
                                if (
                                    hasattr(tool_call_delta, "function")
                                    and tool_call_delta.function
                                ):
                                    if getattr(tool_call_delta.function, "name", None):
                                        current_tool_calls[index]["function"][
                                            "name"
                                        ] = tool_call_delta.function.name

                                    # Accumulate arguments (as string chunks)
                                    if getattr(
                                        tool_call_delta.function, "arguments", None
                                    ):
                                        current_tool_calls[index]["function"][
                                            "arguments"
                                        ] += tool_call_delta.function.arguments

                    # Handle finish reason
                    if getattr(choice, "finish_reason", None):
                        # handle reasoning first
                        reasoning_active_key = f"_reasoning_active"
                        if hasattr(self, reasoning_active_key):
                            if getattr(self, reasoning_active_key) == True:
                                setattr(self, reasoning_active_key, False)
                                yield StreamChunk(type="reasoning_done", content="")

                        if choice.finish_reason == "tool_calls" and current_tool_calls:

                            final_tool_calls = []

                            for index in sorted(current_tool_calls.keys()):
                                call = current_tool_calls[index]
                                function_name = call["function"]["name"]
                                arguments_str = call["function"]["arguments"]

                                try:
                                    arguments_obj = (
                                        json.loads(arguments_str)
                                        if arguments_str.strip()
                                        else {}
                                    )
                                except json.JSONDecodeError:
                                    arguments_obj = {}

                                final_tool_calls.append(
                                    {
                                        "id": call["id"] or f"toolcall_{index}",
                                        "type": "function",
                                        "function": {
                                            "name": function_name,
                                            "arguments": arguments_obj,
                                        },
                                    }
                                )

                            yield StreamChunk(
                                type="tool_calls", tool_calls=final_tool_calls
                            )

                            complete_message = {
                                "role": "assistant",
                                "content": content.strip(),
                                "tool_calls": final_tool_calls,
                            }

                            yield StreamChunk(
                                type="complete_message",
                                complete_message=complete_message,
                            )
                            yield StreamChunk(type="done")
                            return

                        elif choice.finish_reason in ["stop", "length"]:
                            if search_sources_used > 0:
                                yield StreamChunk(
                                    type="content",
                                    content=f"\n✅ [Live Search Complete] Used {search_sources_used} sources\n",
                                )

                            # Handle citations if present
                            if hasattr(chunk, "citations") and chunk.citations:
                                if enable_web_search:
                                    citation_text = "\n📚 **Citations:**\n"
                                    for i, citation in enumerate(chunk.citations, 1):
                                        citation_text += f"{i}. {citation}\n"
                                    yield StreamChunk(
                                        type="content", content=citation_text
                                    )

                            # Return final message
                            complete_message = {
                                "role": "assistant",
                                "content": content.strip(),
                            }
                            yield StreamChunk(
                                type="complete_message",
                                complete_message=complete_message,
                            )
                            yield StreamChunk(type="done")
                            return

                # Optionally handle usage metadata
                if hasattr(chunk, "usage") and chunk.usage:
                    if getattr(chunk.usage, "num_sources_used", 0) > 0:
                        search_sources_used = chunk.usage.num_sources_used
                        if enable_web_search:
                            yield StreamChunk(
                                type="content",
                                content=f"\n📊 [Live Search] Using {search_sources_used} sources for real-time data\n",
                            )

            except Exception as chunk_error:
                yield StreamChunk(
                    type="error", error=f"Chunk processing error: {chunk_error}"
                )
                continue

        # Fallback in case stream ends without finish_reason
        yield StreamChunk(type="done")

    async def stream_with_tools(
        self, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]], **kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """Stream response using OpenAI-compatible Chat Completions API with MCP integration."""
        try:
            import openai

            # Merge constructor config with stream kwargs (stream kwargs take priority)
            all_params = {**self.config, **kwargs}

            # Setup MCP tools if configured
            if MultiMCPClient and self.mcp_servers:
                # Normalize and separate MCP servers
                normalized_servers = self._normalize_mcp_servers(self.mcp_servers)
                self._separate_mcp_servers_by_transport_type(normalized_servers)
                
                # Setup MCP tools (stdio and streamable-HTTP)
                try:
                    await self._setup_mcp_tools()
                    await self._setup_local_http_mcp_tools()
                except Exception as mcp_error:
                    logger.warning(f"MCP setup failed, continuing without MCP: {mcp_error}")

            # Get base_url from config or use OpenAI default
            base_url = all_params.get("base_url", "https://api.openai.com/v1")

            client = openai.AsyncOpenAI(api_key=self.api_key, base_url=base_url)

            # Extract framework-specific parameters
            enable_web_search = all_params.get("enable_web_search", False)
            enable_code_interpreter = all_params.get("enable_code_interpreter", False)

            # Convert tools to Chat Completions format
            converted_tools = (
                self.convert_tools_to_chat_completions_format(tools) if tools else None
            )
            
            # Add MCP tools to the tools list
            mcp_tools = self._convert_mcp_tools_to_chat_completions_format()
            if mcp_tools:
                if converted_tools is None:
                    converted_tools = []
                converted_tools.extend(mcp_tools)

            # Chat Completions API parameters
            api_params = {
                "messages": messages,
                "stream": True,
            }

            # Add tools if provided
            if converted_tools:
                api_params["tools"] = converted_tools

            # Direct passthrough of all parameters except those handled separately
            excluded_params = {
                "enable_web_search",
                "enable_code_interpreter",
                "base_url",
                "agent_id",
                "session_id",
                "type",
                "mcp_servers",
                "allowed_tools",
                "exclude_tools",
            }
            for key, value in all_params.items():
                if key not in excluded_params and value is not None:
                    api_params[key] = value

            # Add provider tools (web search, code interpreter) if enabled
            provider_tools = []
            if enable_web_search:
                provider_tools.append(
                    {
                        "type": "function",
                        "function": {
                            "name": "web_search",
                            "description": "Search the web for current or factual information",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "query": {
                                        "type": "string",
                                        "description": "The search query to send to the web",
                                    }
                                },
                                "required": ["query"],
                            },
                        },
                    }
                )

            if enable_code_interpreter:
                provider_tools.append(
                    {"type": "code_interpreter", "container": {"type": "auto"}}
                )

            if provider_tools:
                if "tools" not in api_params:
                    api_params["tools"] = []
                api_params["tools"].extend(provider_tools)

            # Implement iterative MCP tool execution loop
            current_messages = messages.copy()
            max_iterations = 10  # Prevent infinite loops
            iteration = 0
            
            while iteration < max_iterations:
                iteration += 1
                
                # Create stream with current messages
                stream = await client.chat.completions.create(
                    **{**api_params, "messages": current_messages}
                )
                
                # Process stream and collect tool calls
                tool_calls_made = []
                async for chunk in self.handle_chat_completions_stream(
                    stream, enable_web_search
                ):
                    if chunk.type == "tool_calls":
                        tool_calls_made = chunk.tool_calls
                        yield chunk
                    elif chunk.type == "complete_message":
                        # Add assistant message to conversation
                        current_messages.append(chunk.complete_message)
                        yield chunk
                    elif chunk.type == "done":
                        # Check if we need to execute tools
                        if tool_calls_made:
                            # Check if any of the function calls are NOT MCP functions
                            non_mcp_functions = [call for call in tool_calls_made 
                                               if self.extract_tool_name(call) not in self._mcp_functions]
                            
                            if non_mcp_functions:
                                # If there are non-MCP function calls (like workflow tools),
                                # we need to yield the tool_calls chunk FIRST, then exit the loop
                                # This ensures the orchestrator gets the tool calls to process workflow tools
                                logger.debug(f"Non-MCP function calls detected: {[self.extract_tool_name(call) for call in non_mcp_functions]}. Yielding tool calls to orchestrator.")
                                
                                # The tool_calls chunk was already yielded above when tool_calls_made was set
                                # Just yield done to finish the stream
                                yield chunk
                                return
                            
                            # Execute only MCP tools
                            mcp_tool_calls = [call for call in tool_calls_made 
                                            if self.extract_tool_name(call) in self._mcp_functions]
                            
                            if mcp_tool_calls:
                                tool_results = await self._execute_mcp_tools(mcp_tool_calls)
                                if tool_results:
                                    # Add MCP tool result messages
                                    current_messages.extend(tool_results)
                                    continue  # Continue to next iteration
                        
                        # No tool calls, we're done
                        yield chunk
                        return
                    else:
                        yield chunk
                
                # If we get here without tool calls, we're done
                break

        except Exception as e:
            yield StreamChunk(
                type="error", error=f"Chat Completions API error: {str(e)}"
            )

    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)."""
        # Simple approximation: ~1.3 tokens per word
        return int(len(text.split()) * 1.3)

    def calculate_cost(
        self, input_tokens: int, output_tokens: int, model: str
    ) -> float:
        """Calculate cost for token usage based on OpenAI pricing (default fallback)."""
        model_lower = model.lower()

        # OpenAI GPT-4o pricing (most common)
        if "gpt-4o" in model_lower:
            if "mini" in model_lower:
                input_cost = (input_tokens / 1_000_000) * 0.15
                output_cost = (output_tokens / 1_000_000) * 0.60
            else:
                input_cost = (input_tokens / 1_000_000) * 2.50
                output_cost = (output_tokens / 1_000_000) * 10.00
        # GPT-4 pricing
        elif "gpt-4" in model_lower:
            if "turbo" in model_lower:
                input_cost = (input_tokens / 1_000_000) * 10.00
                output_cost = (output_tokens / 1_000_000) * 30.00
            else:
                input_cost = (input_tokens / 1_000_000) * 30.00
                output_cost = (output_tokens / 1_000_000) * 60.00
        # GPT-3.5 pricing
        elif "gpt-3.5" in model_lower:
            input_cost = (input_tokens / 1_000_000) * 0.50
            output_cost = (output_tokens / 1_000_000) * 1.50
        else:
            # Generic fallback pricing (moderate cost estimate)
            input_cost = (input_tokens / 1_000_000) * 1.00
            output_cost = (output_tokens / 1_000_000) * 3.00

        return input_cost + output_cost

    def extract_tool_name(self, tool_call: Dict[str, Any]) -> str:
        """Extract tool name from Chat Completions format."""
        return tool_call.get("function", {}).get("name", "unknown")

    def extract_tool_arguments(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """Extract tool arguments from Chat Completions format."""
        arguments = tool_call.get("function", {}).get("arguments", {})
        if isinstance(arguments, str):
            try:
                import json

                return json.loads(arguments) if arguments.strip() else {}
            except json.JSONDecodeError:
                return {}
        return arguments

    def extract_tool_call_id(self, tool_call: Dict[str, Any]) -> str:
        """Extract tool call ID from Chat Completions format."""
        return tool_call.get("id", "")

    def create_tool_result_message(
        self, tool_call: Dict[str, Any], result_content: str
    ) -> Dict[str, Any]:
        """Create tool result message for Chat Completions format."""
        tool_call_id = self.extract_tool_call_id(tool_call)
        return {
            "role": "tool",
            "tool_call_id": tool_call_id,
            "content": result_content,
        }

    def extract_tool_result_content(self, tool_result_message: Dict[str, Any]) -> str:
        """Extract content from Chat Completions tool result message."""
        return tool_result_message.get("content", "")

    def get_supported_builtin_tools(self) -> List[str]:
        """Get list of builtin tools supported by this provider."""
        # Chat Completions API doesn't typically support builtin tools like web_search
        # But some providers might - this can be overridden in subclasses
        return []
    
    async def disconnect(self) -> None:
        """Properly disconnect MCP clients and reset state."""
        try:
            # Disconnect stdio MCP client
            if self._mcp_client:
                try:
                    await self._mcp_client.disconnect()
                except Exception as e:
                    logger.warning(f"Error disconnecting stdio MCP client: {e}")
                finally:
                    self._mcp_client = None
            
            # Disconnect streamable-HTTP MCP client
            if self._streamable_mcp_client:
                try:
                    await self._streamable_mcp_client.disconnect()
                except Exception as e:
                    logger.warning(f"Error disconnecting streamable-HTTP MCP client: {e}")
                finally:
                    self._streamable_mcp_client = None
            
            # Reset initialization flags
            self._mcp_initialized = False
            self._streamable_http_initialized = False
            
            # Clear function registry
            self._mcp_functions.clear()
            
            # Reset statistics counters
            if self._stats_lock:
                async with self._stats_lock:
                    self._mcp_stats = {
                        'tool_calls': 0,
                        'failures': 0,
                        'stdio_failures': 0,
                        'streamable_http_failures': 0
                    }
            
            logger.info("MCP clients disconnected successfully")
            
        except Exception as e:
            logger.error(f"Error during MCP cleanup: {e}")
    
    async def _handle_mcp_error_and_fallback(self, error: Exception, operation: str) -> AsyncGenerator[StreamChunk, None]:
        """Handle MCP errors and fallback to non-MCP streaming."""
        error_type, error_msg = self._get_mcp_error_info(error)
        
        # Update failure statistics
        if self._stats_lock:
            async with self._stats_lock:
                self._mcp_stats['failures'] += 1
        
        # Yield user-friendly error message
        if error_type == "connection":
            yield StreamChunk(
                type="content",
                content="⚠️ MCP connection failed. Continuing without MCP tools.\n"
            )
        elif error_type == "timeout":
            yield StreamChunk(
                type="content",
                content="⚠️ MCP operation timed out. Continuing without MCP tools.\n"
            )
        elif error_type == "configuration":
            yield StreamChunk(
                type="content",
                content="⚠️ MCP configuration error. Continuing without MCP tools.\n"
            )
        else:
            yield StreamChunk(
                type="content",
                content="⚠️ MCP error occurred. Continuing without MCP tools.\n"
            )
        
        # Log the error
        self._log_mcp_error(operation, error)
        
        # Clear MCP tools from function registry to prevent further attempts
        self._mcp_functions.clear()

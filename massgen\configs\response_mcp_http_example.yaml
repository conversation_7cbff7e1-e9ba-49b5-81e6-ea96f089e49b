# MassGen Configuration: OpenAI Response API with HTTP MCP Integration
# Usage:
#   uv run python -m massgen.cli --config configs/response_mcp_http_example.yaml "Test the MCP tools"

agents:
  - id: "response_mcp_http_test"
    backend:
      type: "openai"
      model: "gpt-4o-mini"
      # HTTP MCP servers configuration for ResponseBackend
      mcp_servers:
        # HTTP MCP server example (requires a running MCP server)
        - name: "http_test_server"
          type: "streamable-http"
          url: "https://original-slug.fastmcp.app/mcp"
      
    system_message: |
      You are an AI assistant with access to MCP tools through OpenAI's Response API.
      
      You have access to HTTP MCP tools that can be called directly by OpenAI.
      
      Use these tools as needed to answer user questions comprehensively.

ui:
  display_type: "rich_terminal"
  logging_enabled: true
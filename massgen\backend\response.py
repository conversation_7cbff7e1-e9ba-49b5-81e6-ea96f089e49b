"""
Response API backend implementation.
Standalone implementation optimized for the standard Response API format (originated by OpenAI).
"""
from __future__ import annotations
import os
import logging
import json
import asyncio
from typing import Dict, List, Any, AsyncGenerator, Optional, Callable
from .base import LLMBackend, StreamChunk

logger = logging.getLogger(__name__)

# MCP integration imports
try:
    from ..mcp_tools import MultiMCPClient, MCPError, MCPConnectionError
    from ..mcp_tools.config_validator import MCPConfigValidator
    from ..mcp_tools.circuit_breaker import MCPCircuitBreaker
    from ..mcp_tools.exceptions import (
        MCPConfigurationError,
        MCPValidationError,
        MCPTimeoutError,
        MCPServerError,
    )
    from ..mcp_tools.security import validate_url
except ImportError:  # MCP not installed or import failed within mcp_tools
    MultiMCPClient = None  # type: ignore[assignment]
    MCPError = ImportError  # type: ignore[assignment]
    MCPConnectionError = ImportError  # type: ignore[assignment]
    MCPConfigValidator = None  # type: ignore[assignment]
    MCPCircuitBreaker = None  # type: ignore[assignment]
    MCPConfigurationError = ImportError  # type: ignore[assignment]
    MCPValidationError = ImportError  # type: ignore[assignment]
    MCPTimeoutError = ImportError  # type: ignore[assignment]
    MCPServerError = ImportError  # type: ignore[assignment]
    validate_url = None


class Function:
    """Function wrapper for MCP tools that can be called by OpenAI Response API."""

    def __init__(self, name: str, description: str, parameters: Dict[str, Any], entrypoint: Callable[[str], Any]) -> None:
        self.name = name
        self.description = description
        self.parameters = parameters
        self.entrypoint = entrypoint

    async def call(self, input_str: str) -> Any:
        """Call the function with input string."""
        return await self.entrypoint(input_str)

    def to_openai_format(self) -> Dict[str, Any]:
        """Convert function to OpenAI tool format."""
        return {
            "type": "function",
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
        }


class ResponseBackend(LLMBackend):
    """Backend using the standard Response API format."""

    def __init__(self, api_key: Optional[str] = None, **kwargs):
        super().__init__(api_key, **kwargs)
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")

        # MCP integration
        self.mcp_servers = kwargs.pop("mcp_servers", [])
        self.allowed_tools = kwargs.pop("allowed_tools", None)
        self.exclude_tools = kwargs.pop("exclude_tools", None)
        self._mcp_client: Optional[MultiMCPClient] = None
        self._mcp_initialized = False

        # Streamable HTTP MCP integration (treated like stdio tools with local execution)
        self._streamable_mcp_client: Optional[MultiMCPClient] = None
        self._streamable_http_initialized = False

        # MCP tool execution monitoring
        self._mcp_tool_calls_count = 0
        self._mcp_tool_failures = 0

        # Circuit breakers for different transport types
        self._stdio_circuit_breaker = MCPCircuitBreaker() if MCPCircuitBreaker else None
        self._http_circuit_breaker = MCPCircuitBreaker() if MCPCircuitBreaker else None
        self._streamable_http_circuit_breaker = MCPCircuitBreaker() if MCPCircuitBreaker else None

        # Separation containers for different transport types
        # Transport Types:
        # - "stdio": Local process-based MCP servers
        # - "http": Native OpenAI HTTP MCP servers (direct OpenAI ↔ External server)
        # - "streamable-http": Local HTTP MCP with streaming execution (local control like stdio)
        self._stdio_servers: List[Dict[str, Any]] = []        # Local process MCP servers
        self._http_servers: List[Dict[str, Any]] = []         # Native OpenAI HTTP MCP servers  
        self._streamable_http_servers: List[Dict[str, Any]] = []   # Streamable HTTP MCP with local execution

        # Function registry for stdio MCP tools
        self.functions: Dict[str, Function] = {}

        # Thread safety for counters
        self._stats_lock = asyncio.Lock()

        # Separate MCP servers by transport type if any are configured
        if self.mcp_servers:
            self._separate_mcp_servers_by_transport_type()

    def _normalize_mcp_servers(self) -> List[Dict[str, Any]]:
        """Validate and normalize mcp_servers into a list of dicts."""
        servers = self.mcp_servers
        if not servers:
            return []
        if isinstance(servers, dict):
            return [servers]
        if not isinstance(servers, list):
            raise ValueError(
                f"mcp_servers must be a list or dict, got {type(servers).__name__}"
            )
        normalized: List[Dict[str, Any]] = []
        for idx, entry in enumerate(servers):
            if not isinstance(entry, dict):
                raise ValueError(
                    f"MCP server configuration at index {idx} must be a dictionary, got {type(entry).__name__}"
                )
            normalized.append(entry)
        return normalized

    def _separate_mcp_servers_by_transport_type(self) -> None:
        """
        Separate MCP servers into stdio, HTTP, and streamable HTTP transport types.
        
        Transport Types:
        - "stdio": Local process-based MCP servers (spawned via command/args)
        - "http": Native OpenAI HTTP MCP servers (direct OpenAI ↔ External server)
        - "streamable-http": Local HTTP MCP with streaming execution (local control like stdio)
        """
        validated_servers = self._normalize_mcp_servers()

        for server in validated_servers:
            transport_type = server.get("type")
            server_name = server.get("name", "unnamed")

            if not transport_type:
                logger.warning(
                    f"MCP server '{server_name}' missing required 'type' field. "
                    f"Supported types: 'stdio', 'http', 'streamable-http'. Skipping server."
                )
                continue

            if transport_type == "stdio":
                self._stdio_servers.append(server)
            elif transport_type == "http":  # Native OpenAI HTTP MCP
                self._http_servers.append(server)
            elif transport_type == "streamable-http":  # Streamable HTTP MCP with local execution
                self._streamable_http_servers.append(server)
            else:
                logger.warning(
                    f"Unknown MCP transport type '{transport_type}' for server '{server_name}'. "
                    f"Supported types: 'stdio', 'http', 'streamable-http'. Skipping server."
                )

    @staticmethod
    def _get_mcp_error_info(error: Exception) -> tuple[str, str, str]:
        """Get standardized MCP error information.

        Returns:
            tuple: (log_type, user_message, error_category)
        """
        error_mappings = {
            MCPConnectionError: ("connection error", "MCP connection failed", "connection"),
            MCPTimeoutError: ("timeout error", "MCP session timeout", "timeout"),
            MCPServerError: ("server error", "MCP server error", "server"),
            MCPError: ("MCP error", "MCP error", "general"),
        }

        return error_mappings.get(
            type(error), ("unexpected error", "MCP communication error", "unknown")
        )

    def _log_mcp_error(self, error: Exception, context: str) -> None:
        """Log MCP errors with specific error type messaging."""
        log_type, _, _ = self._get_mcp_error_info(error)
        logger.warning(f"MCP {log_type} during {context}: {error}")

    async def _handle_mcp_error_and_fallback(
        self,
        error: Exception,
        api_params: Dict[str, Any],
        provider_tools: List[Dict[str, Any]],
        stream_func: Callable,
    ) -> AsyncGenerator[StreamChunk, None]:
        """Handle MCP errors with specific messaging and fallback to non-MCP tools."""
        # Thread safety for counter updates
        async with self._stats_lock:
            self._mcp_tool_failures += 1

        log_type, user_message, _ = self._get_mcp_error_info(error)

        # Log with specific error type
        logger.warning(
            f"MCP tool call #{self._mcp_tool_calls_count} failed - {log_type}: {error}"
        )

        # Yield user-friendly error message
        yield StreamChunk(
            type="content",
            content=f"\n⚠️  {user_message} ({error}); continuing without MCP tools\n",
        )

        # Build non-MCP configuration and stream fallback
        fallback_params = dict(api_params)

        # Remove any HTTP MCP tools from the tools list
        if "tools" in fallback_params:
            non_mcp_tools = [
                tool
                for tool in fallback_params["tools"]
                if tool.get("type") != "mcp"
            ]
            fallback_params["tools"] = non_mcp_tools

        # Add back provider tools if they were present
        if provider_tools:
            if "tools" not in fallback_params:
                fallback_params["tools"] = []
            fallback_params["tools"].extend(provider_tools)

        async for chunk in stream_func(fallback_params):
            yield chunk

    async def _setup_mcp_tools(self) -> None:
        """Initialize MCP client for stdio servers only."""
        if not self._stdio_servers or self._mcp_initialized:
            return

        if MultiMCPClient is None:
            reason = "MCP import failed - MultiMCPClient not available"
            logger.warning(
                "MCP support import failed (%s). stdio mcp_servers were provided; falling back to workflow tools without MCP. Ensure the 'mcp' package is installed and compatible with this codebase.",
                reason,
            )
            # Clear stdio servers to prevent further attempts
            self._stdio_servers = []
            return

        try:
            # Extract tool filtering parameters
            allowed_tools = self.allowed_tools
            exclude_tools = self.exclude_tools

            # Validate MCP configuration before initialization
            if MCPConfigValidator is not None:
                try:
                    backend_config = {
                        "mcp_servers": self._stdio_servers,
                        "allowed_tools": self.allowed_tools,
                        "exclude_tools": self.exclude_tools,
                    }

                    # Use the comprehensive validator class for enhanced validation
                    validator = MCPConfigValidator()
                    validated_config = validator.validate_backend_mcp_config(backend_config)

                    self._stdio_servers = validated_config.get("mcp_servers", self._stdio_servers)

                    # Extract validated tool filtering parameters
                    allowed_tools = validated_config.get("allowed_tools", self.allowed_tools)
                    exclude_tools = validated_config.get("exclude_tools", self.exclude_tools)

                    logger.debug(
                        f"MCP configuration validation passed for {len(self._stdio_servers)} stdio servers"
                    )

                except MCPConfigurationError as e:
                    logger.error(f"MCP configuration validation failed: {e.original_message}")
                    self._mcp_client = None
                    raise RuntimeError(
                        f"Invalid MCP configuration: {e.original_message}"
                    ) from e
                except MCPValidationError as e:
                    logger.error(f"MCP validation failed: {e.original_message}")
                    self._mcp_client = None
                    raise RuntimeError(f"MCP validation error: {e.original_message}") from e
                except Exception as e:
                    if isinstance(e, (ImportError, AttributeError)):
                        logger.debug(f"MCP validation not available: {e}")
                    else:
                        logger.warning(f"MCP validation error: {e}")
                        self._mcp_client = None
                        raise RuntimeError(
                            f"MCP configuration validation failed: {e}"
                        ) from e
            else:
                logger.debug("MCP validation not available, proceeding without validation")

            logger.info(
                f"Setting up MCP sessions with {len(self._stdio_servers)} stdio servers"
            )

            # Log tool filtering if configured
            if allowed_tools:
                logger.info(f"MCP tool filtering - allowed tools: {allowed_tools}")
            if exclude_tools:
                logger.info(f"MCP tool filtering - excluding: {exclude_tools}")

            # Create MCP client connection with retry logic and circuit breaker
            max_mcp_retries = 3
            mcp_connected = False

            for retry_count in range(1, max_mcp_retries + 1):
                try:
                    if retry_count > 1:
                        logger.info(f"MCP connection retry {retry_count}/{max_mcp_retries}")
                        await asyncio.sleep(0.5 * retry_count)  # Progressive backoff

                    # Check circuit breaker for stdio servers
                    filtered_stdio_servers = []
                    if self._stdio_circuit_breaker:
                        for server in self._stdio_servers:
                            server_name = server.get("name", "unnamed")
                            if not self._stdio_circuit_breaker.should_skip_server(server_name):
                                filtered_stdio_servers.append(server)
                            else:
                                logger.debug(f"Skipping stdio server {server_name} due to circuit breaker")
                    else:
                        filtered_stdio_servers = self._stdio_servers

                    if not filtered_stdio_servers:
                        logger.warning("All stdio servers are circuit breaker blocked")
                        break

                    self._mcp_client = await MultiMCPClient.create_and_connect(
                        filtered_stdio_servers,
                        timeout_seconds=30,
                        allowed_tools=allowed_tools,
                        exclude_tools=exclude_tools,
                    )

                    # Record success for circuit breaker
                    if self._stdio_circuit_breaker:
                        for server in filtered_stdio_servers:
                            server_name = server.get("name", "unnamed")
                            self._stdio_circuit_breaker.record_success(server_name)

                    mcp_connected = True
                    logger.info(f"MCP connection successful on attempt {retry_count}")
                    break

                except (MCPConnectionError, MCPTimeoutError, MCPServerError, MCPError) as e:
                    # Record failure for circuit breaker
                    if self._stdio_circuit_breaker:
                        for server in self._stdio_servers:
                            server_name = server.get("name", "unnamed")
                            self._stdio_circuit_breaker.record_failure(server_name)

                    if retry_count >= max_mcp_retries:
                        logger.warning(f"MCP connection failed after {max_mcp_retries} attempts: {e}")
                        self._mcp_client = None
                        return  # Graceful fallback - don't raise exception
                    else:
                        logger.warning(f"MCP connection attempt {retry_count} failed: {e}")

            if not mcp_connected:
                logger.info("Falling back to workflow tools after MCP connection failures")
                return

            # Register tools from the client as Functions
            for tool_name, tool in self._mcp_client.tools.items():
                try:
                    # Fix closure bug by using default parameter to capture tool_name
                    def create_tool_entrypoint(captured_tool_name: str = tool_name):
                        async def tool_entrypoint(input_str: str) -> Any:
                            return await self._mcp_client.call_tool(
                                captured_tool_name, json.loads(input_str)
                            )
                        return tool_entrypoint

                    # Create the entrypoint with captured tool name
                    entrypoint = create_tool_entrypoint()

                    # Create a Function for the tool
                    function = Function(
                        name=tool_name,
                        description=tool.description,
                        parameters=tool.inputSchema,
                        entrypoint=entrypoint,
                    )

                    # Register the Function
                    self.functions[function.name] = function
                    logger.debug(f"Function: {function.name} registered")
                except Exception as e:
                    logger.error(f"Failed to register tool {tool_name}: {e}")

            self._mcp_initialized = True
            logger.info(
                f"Successfully initialized MCP stdio sessions with {len(self._mcp_client.tools)} tools converted to functions"
            )

        except Exception as e:
            # Enhanced error handling for different MCP error types
            if isinstance(e, RuntimeError) and "MCP configuration" in str(e):
                raise
            elif isinstance(e, MCPConnectionError):
                logger.error(f"MCP connection failed during setup: {e}")
                self._mcp_client = None
                raise RuntimeError(f"Failed to establish MCP connections: {e}") from e
            elif isinstance(e, MCPTimeoutError):
                logger.error(f"MCP connection timed out during setup: {e}")
                self._mcp_client = None
                raise RuntimeError(f"MCP connection timeout: {e}") from e
            elif isinstance(e, MCPServerError):
                logger.error(f"MCP server error during setup: {e}")
                self._mcp_client = None
                raise RuntimeError(f"MCP server error: {e}") from e
            elif isinstance(e, MCPError):
                logger.warning(f"MCP error during setup: {e}")
                self._mcp_client = None
                return
            elif "MCP" in str(type(e).__name__):
                logger.warning(f"Unknown MCP-specific error during setup: {e}")
                self._mcp_client = None
                return
            else:
                logger.warning(f"Failed to setup MCP sessions: {e}")
                self._mcp_client = None

    async def _setup_streamable_http_mcp_tools(self) -> None:
        """
        Initialize MCP client for streamable HTTP servers (treated like stdio functions).
        
        These servers use 'streamable-http' type and provide:
        - Local execution with full control (like stdio servers)
        - Circuit breaker and error handling
        - Request/response logging
        - Function-style execution for OpenAI integration
        - HTTP communication instead of process spawning
        """
        if not self._streamable_http_servers or self._streamable_http_initialized:
            return

        if MultiMCPClient is None:
            reason = "MCP import failed - MultiMCPClient not available"
            logger.warning(
                "MCP support import failed (%s). streamable-http servers were provided; continuing without streamable HTTP MCP.",
                reason,
            )
            # Clear to prevent further attempts
            self._streamable_http_servers = []
            return

        try:
            allowed_tools = self.allowed_tools
            exclude_tools = self.exclude_tools

            # Validate/Filter URLs for streamable HTTP usage if validator exists and circuit-break failing ones
            filtered_streamable_servers: List[Dict[str, Any]] = []
            for server in self._streamable_http_servers:
                server_name = server.get("name", "unnamed")
                if self._streamable_http_circuit_breaker and self._streamable_http_circuit_breaker.should_skip_server(server_name):
                    logger.debug(f"Skipping streamable HTTP server {server_name} due to circuit breaker")
                    continue

                url = server.get("url")
                if not url:
                    logger.warning(f"Streamable HTTP MCP server {server_name} missing URL, skipping")
                    if self._streamable_http_circuit_breaker:
                        self._streamable_http_circuit_breaker.record_failure(server_name)
                    continue

                if validate_url is not None:
                    try:
                        validate_url(url, allow_localhost=True, allow_private_ips=True)
                    except ValueError as e:
                        logger.warning(f"Invalid URL for streamable HTTP MCP server {server_name}: {e}")
                        if self._streamable_http_circuit_breaker:
                            self._streamable_http_circuit_breaker.record_failure(server_name)
                        continue

                # Normalize transport type for MCPClient: it only supports 'streamable-http'
                normalized = dict(server)
                normalized["type"] = "streamable-http"
                filtered_streamable_servers.append(normalized)

            if not filtered_streamable_servers:
                logger.warning("All streamable-http servers filtered out or circuit-breaker blocked")
                return

            logger.info(
                f"Setting up MCP sessions with {len(filtered_streamable_servers)} streamable-http servers"
            )

            # Connect via MultiMCPClient (uses HTTP transport under the hood)
            max_retries = 3
            connected = False
            for attempt in range(1, max_retries + 1):
                try:
                    if attempt > 1:
                        logger.info(f"Streamable HTTP MCP connection retry {attempt}/{max_retries}")
                        await asyncio.sleep(0.5 * attempt)

                    self._streamable_mcp_client = await MultiMCPClient.create_and_connect(
                        filtered_streamable_servers,
                        timeout_seconds=30,
                        allowed_tools=allowed_tools,
                        exclude_tools=exclude_tools,
                    )

                    # Record success
                    if self._streamable_http_circuit_breaker:
                        for s in self._streamable_http_servers:
                            server_name = s.get("name", "unnamed")
                            self._streamable_http_circuit_breaker.record_success(server_name)

                    connected = True
                    break
                except (MCPConnectionError, MCPTimeoutError, MCPServerError, MCPError) as e:
                    if self._streamable_http_circuit_breaker:
                        for s in self._streamable_http_servers:
                            server_name = s.get("name", "unnamed")
                            self._streamable_http_circuit_breaker.record_failure(server_name)
                    if attempt >= max_retries:
                        logger.warning(f"Streamable HTTP MCP connection failed after {max_retries} attempts: {e}")
                        self._streamable_mcp_client = None
                        return
                    else:
                        logger.warning(f"Streamable HTTP MCP connection attempt {attempt} failed: {e}")

            if not connected:
                logger.info("Falling back after streamable HTTP MCP connection failures")
                return

            # Register tools from the streamable client as Functions (identical pathway to stdio)
            for tool_name, tool in self._streamable_mcp_client.tools.items():
                try:
                    def create_tool_entrypoint(captured_tool_name: str = tool_name):
                        async def tool_entrypoint(input_str: str) -> Any:
                            # Use streamable HTTP MCP client to call tool with JSON args
                            return await self._streamable_mcp_client.call_tool(
                                captured_tool_name, json.loads(input_str)
                            )
                        return tool_entrypoint

                    entrypoint = create_tool_entrypoint()
                    function = Function(
                        name=tool_name,
                        description=tool.description,
                        parameters=tool.inputSchema,
                        entrypoint=entrypoint,
                    )
                    self.functions[function.name] = function
                    logger.debug(f"Function: {function.name} (streamable-http) registered")
                except Exception as e:
                    logger.error(f"Failed to register streamable HTTP tool {tool_name}: {e}")

            self._streamable_http_initialized = True
            logger.info(
                f"Initialized streamable-http MCP sessions with {len(self._streamable_mcp_client.tools)} tools converted to functions"
            )

        except Exception as e:
            if isinstance(e, MCPConnectionError):
                logger.error(f"Streamable HTTP MCP connection failed during setup: {e}")
                self._streamable_mcp_client = None
                raise RuntimeError(f"Failed to establish streamable HTTP MCP connections: {e}") from e
            elif isinstance(e, MCPTimeoutError):
                logger.error(f"Streamable HTTP MCP connection timed out during setup: {e}")
                self._streamable_mcp_client = None
                raise RuntimeError(f"Streamable HTTP MCP connection timeout: {e}") from e
            elif isinstance(e, MCPServerError):
                logger.error(f"Streamable HTTP MCP server error during setup: {e}")
                self._streamable_mcp_client = None
                raise RuntimeError(f"Streamable HTTP MCP server error: {e}") from e
            elif isinstance(e, MCPError):
                logger.warning(f"Streamable HTTP MCP error during setup: {e}")
                self._streamable_mcp_client = None
                return
            else:
                logger.warning(f"Failed to setup streamable HTTP MCP sessions: {e}")
                self._streamable_mcp_client = None

    def _convert_stdio_servers_to_openai_format(self) -> List[Dict[str, Any]]:
        """Convert stdio MCP tools to OpenAI function declarations."""
        if not self.functions:
            return []

        converted_tools = []
        for function in self.functions.values():
            converted_tools.append(function.to_openai_format())

        logger.debug(
            f"Converted {len(converted_tools)} stdio MCP tools to OpenAI format"
        )
        return converted_tools

    def _convert_http_servers_to_openai_format(self) -> List[Dict[str, Any]]:
        """Convert HTTP MCP servers to OpenAI native MCP format."""
        converted_servers = []

        for server in self._http_servers:
            server_name = server.get("name", "unnamed")

            # Apply circuit breaker - skip if server is currently tripped
            if self._http_circuit_breaker and self._http_circuit_breaker.should_skip_server(server_name):
                logger.debug(f"Skipping HTTP MCP server {server_name} due to circuit breaker")
                continue

            server_url = server.get("url")
            if not server_url:
                logger.warning(
                    f"HTTP MCP server {server_name} missing URL, skipping"
                )
                continue

            # Validate URL if validate_url is available
            if validate_url is not None:
                try:
                    # Allow localhost/private IPs to support dev/test environments
                    validate_url(
                        server_url,
                        allow_localhost=True,
                        allow_private_ips=True
                    )
                except ValueError as e:
                    logger.warning(f"Invalid URL for HTTP MCP server {server_name}: {e}")
                    if self._http_circuit_breaker:
                        self._http_circuit_breaker.record_failure(server_name)
                    continue

            # Convert to OpenAI native MCP format
            openai_server = {
                "type": "mcp",
                "server_label": server_name,
                "server_url": server_url,
                "require_approval": "never",
            }

            # Add allowed_tools if present
            if "allowed_tools" in server:
                openai_server["allowed_tools"] = server["allowed_tools"]

            converted_servers.append(openai_server)

        logger.debug(
            f"Converted {len(converted_servers)} HTTP MCP servers to OpenAI format"
        )
        return converted_servers

    def _build_response_api_params(
        self, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]], all_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Build OpenAI Response API parameters with MCP integration."""
        # Convert messages to Response API format
        converted_messages = self.convert_messages_to_response_api_format(messages)

        # Response API parameters (uses 'input', not 'messages')
        api_params = {"input": converted_messages, "stream": True}

        # Direct passthrough of all parameters except those handled separately
        excluded_params = {
            "enable_web_search",
            "enable_code_interpreter",
            "agent_id",
            "session_id",
            "type",  # Used for MCP server configuration, not for OpenAI API
            "mcp_servers",  # MCP-specific parameter
            "allowed_tools",  # Tool filtering parameter
            "exclude_tools",  # Tool filtering parameter
        }
        for key, value in all_params.items():
            if key not in excluded_params and value is not None:
                # Handle OpenAI Response API parameter name differences
                if key == "max_tokens":
                    api_params["max_output_tokens"] = value
                else:
                    api_params[key] = value

        # Add framework tools (convert to Response API format)
        if tools:
            converted_tools = self.convert_tools_to_response_api_format(tools)
            api_params["tools"] = converted_tools

        # Add stdio MCP tools as functions
        if self.functions:
            stdio_tools = self._convert_stdio_servers_to_openai_format()
            if stdio_tools:
                if "tools" not in api_params:
                    api_params["tools"] = []
                api_params["tools"].extend(stdio_tools)
                logger.debug(f"Added {len(stdio_tools)} stdio MCP tools to OpenAI Response API")

        # Add HTTP MCP servers as native MCP tools
        if self._http_servers:
            http_mcp_tools = self._convert_http_servers_to_openai_format()
            if http_mcp_tools:
                if "tools" not in api_params:
                    api_params["tools"] = []
                api_params["tools"].extend(http_mcp_tools)
                logger.debug(f"Added {len(http_mcp_tools)} HTTP MCP servers to OpenAI tools")

        # Add provider tools (web search, code interpreter) if enabled
        provider_tools = []
        enable_web_search = all_params.get("enable_web_search", False)
        enable_code_interpreter = all_params.get("enable_code_interpreter", False)

        if enable_web_search:
            provider_tools.append({"type": "web_search"})
        if enable_code_interpreter:
            provider_tools.append(
                {"type": "code_interpreter", "container": {"type": "auto"}}
            )

        if provider_tools:
            if "tools" not in api_params:
                api_params["tools"] = []
            api_params["tools"].extend(provider_tools)

        return api_params

    def convert_tools_to_response_api_format(
        self, tools: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert tools from Chat Completions format to Response API format if needed.

        Chat Completions format: {"type": "function", "function": {"name": ..., "description": ..., "parameters": ...}}
        Response API format: {"type": "function", "name": ..., "description": ..., "parameters": ...}
        """
        if not tools:
            return tools

        converted_tools = []
        for tool in tools:
            if tool.get("type") == "function" and "function" in tool:
                # Chat Completions format - convert to Response API format
                func = tool["function"]
                converted_tools.append(
                    {
                        "type": "function",
                        "name": func["name"],
                        "description": func["description"],
                        "parameters": func.get("parameters", {}),
                    }
                )
            else:
                # Already in Response API format or non-function tool
                converted_tools.append(tool)

        return converted_tools

    def convert_messages_to_response_api_format(
        self, messages: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert messages from Chat Completions format to Response API format.

        Chat Completions tool message: {"role": "tool", "tool_call_id": "...", "content": "..."}
        Response API tool message: {"type": "function_call_output", "call_id": "...", "output": "..."}

        Note: Assistant messages with tool_calls should not be in input - they're generated by the backend.
        """
        # First pass: remove 'status' from messages without 'role'
        cleaned_messages = []
        for message in messages:
            if "status" in message and "role" not in message:
                # Create a copy without 'status'
                cleaned_message = {k: v for k, v in message.items() if k != "status"}
                cleaned_messages.append(cleaned_message)
            else:
                cleaned_messages.append(message)

        converted_messages = []

        for message in cleaned_messages:
            if message.get("role") == "tool":
                # Convert Chat Completions tool message to Response API format
                converted_message = {
                    "type": "function_call_output",
                    "call_id": message.get("tool_call_id"),
                    "output": message.get("content", ""),
                }
                converted_messages.append(converted_message)
            elif message.get("type") == "function_call_output":
                # Already in Response API format
                converted_messages.append(message)
            elif message.get("role") == "assistant" and "tool_calls" in message:
                # Assistant message with tool_calls - remove tool_calls when sending as input
                # Only results should be sent back, not the original tool calls
                cleaned_message = {
                    k: v for k, v in message.items() if k != "tool_calls"
                }
                converted_messages.append(cleaned_message)
            else:
                # For other message types, pass through as-is
                converted_messages.append(message.copy())

        return converted_messages

    async def _stream_with_stdio_mcp_execution(
        self, client, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]], **kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """Stream response with stdio MCP function call execution loop."""
        max_iterations = 10  # Prevent infinite loops
        current_messages = messages.copy()

        for iteration in range(max_iterations):
            logger.debug(f"MCP function call iteration {iteration + 1}/{max_iterations}")

            # Build API params for this iteration
            all_params = {**self.config, **kwargs}
            api_params = self._build_response_api_params(current_messages, tools, all_params)

            # Start streaming
            stream = await client.responses.create(**api_params)

            # Track function calls in this iteration
            captured_function_calls = []
            current_function_call = None
            content = ""
            response_completed = False

            async for chunk in stream:
                if hasattr(chunk, "type"):
                    # Detect function call start
                    if (chunk.type == "response.output_item.added" and
                        hasattr(chunk, "item") and chunk.item and
                        getattr(chunk.item, "type", None) == "function_call"):

                        current_function_call = {
                            "call_id": getattr(chunk.item, "call_id", ""),
                            "name": getattr(chunk.item, "name", ""),
                            "arguments": ""
                        }
                        logger.debug(f"Function call detected: {current_function_call['name']}")

                    # Accumulate function arguments
                    elif (chunk.type == "response.function_call_arguments.delta" and
                          current_function_call is not None):
                        delta = getattr(chunk, "delta", "")
                        current_function_call["arguments"] += delta

                    # Function call completed
                    elif (chunk.type == "response.output_item.done" and
                          current_function_call is not None):
                        captured_function_calls.append(current_function_call)
                        current_function_call = None

                    # Handle regular content and other events
                    elif chunk.type == "response.output_text.delta":
                        delta = getattr(chunk, "delta", "")
                        content += delta
                        yield StreamChunk(type="content", content=delta)

                    # Handle other streaming events (reasoning, provider tools, etc.)
                    else:
                        # Pass through other chunk types
                        yield self._process_stream_chunk(chunk)

                    # Response completed
                    if chunk.type == "response.completed":
                        response_completed = True
                        if captured_function_calls:
                            # Execute captured function calls
                            break  # Exit chunk loop to execute functions
                        else:
                            # No function calls, we're done
                            yield StreamChunk(type="done")
                            return

            # Execute any captured function calls
            if captured_function_calls:
                # Check if any of the function calls are NOT MCP functions
                non_mcp_functions = [call for call in captured_function_calls if call["name"] not in self.functions]

                if non_mcp_functions:
                    # If there are non-MCP function calls (like workflow tools),
                    # exit the MCP execution loop and let the normal workflow handle them
                    logger.debug(f"Non-MCP function calls detected: {[call['name'] for call in non_mcp_functions]}. Exiting MCP execution loop.")
                    yield StreamChunk(type="done")
                    return

                # Execute only MCP function calls
                mcp_functions_executed = False
                for call in captured_function_calls:
                    try:
                        function_name = call["name"]
                        if function_name in self.functions:
                            # Execute the stdio MCP function
                            result = await self.functions[function_name].call(call["arguments"])

                            # Add both the function call and the function call output to messages
                            # This is required by OpenAI Response API format
                            current_messages.append({
                                "type": "function_call",
                                "call_id": call["call_id"],
                                "name": function_name,
                                "arguments": call["arguments"]
                            })
                            current_messages.append({
                                "type": "function_call_output",
                                "call_id": call["call_id"],
                                "output": str(result)
                            })

                            logger.debug(f"Executed stdio MCP function {function_name}")
                            mcp_functions_executed = True

                    except Exception as e:
                        logger.error(f"Error executing MCP function {call['name']}: {e}")
                        # Add error result
                        current_messages.append({
                            "type": "function_call_output",
                            "call_id": call["call_id"],
                            "output": f"Error: {str(e)}"
                        })

                # After executing MCP functions, continue to next iteration to get the final response
                if mcp_functions_executed:
                    continue
                else:
                    # No MCP functions were executed, exit the loop
                    yield StreamChunk(type="done")
                    return
            elif response_completed:
                # Response completed with no function calls - we're truly done
                yield StreamChunk(type="done")
                return
            else:
                # No function calls and response not completed - continue
                continue

        # Max iterations reached
        logger.warning(f"Max MCP function call iterations ({max_iterations}) reached")
        yield StreamChunk(type="done")

    async def _stream_without_stdio_mcp(
        self, client, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]], **kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """Stream response without stdio MCP function execution (HTTP-only or no MCP)."""
        all_params = {**self.config, **kwargs}
        api_params = self._build_response_api_params(messages, tools, all_params)

        stream = await client.responses.create(**api_params)

        async for chunk in stream:
            yield self._process_stream_chunk(chunk)

    def _process_stream_chunk(self, chunk) -> StreamChunk:
        """Process individual stream chunks and convert to StreamChunk format."""
        if not hasattr(chunk, "type"):
            return StreamChunk(type="content", content="")

        chunk_type = chunk.type

        # Handle different chunk types
        if chunk_type == "response.output_text.delta" and hasattr(chunk, "delta"):
            return StreamChunk(type="content", content=chunk.delta)

        elif chunk_type == "response.reasoning_text.delta" and hasattr(chunk, "delta"):
            return StreamChunk(
                type="reasoning",
                content=f"🧠 [Reasoning] {chunk.delta}",
                reasoning_delta=chunk.delta,
                item_id=getattr(chunk, "item_id", None),
                content_index=getattr(chunk, "content_index", None),
            )

        elif chunk_type == "response.reasoning_text.done":
            reasoning_text = getattr(chunk, "text", "")
            return StreamChunk(
                type="reasoning_done",
                content=f"\n🧠 [Reasoning Complete]\n",
                reasoning_text=reasoning_text,
                item_id=getattr(chunk, "item_id", None),
                content_index=getattr(chunk, "content_index", None),
            )

        elif chunk_type == "response.reasoning_summary_text.delta" and hasattr(chunk, "delta"):
            return StreamChunk(
                type="reasoning_summary",
                content=chunk.delta,
                reasoning_summary_delta=chunk.delta,
                item_id=getattr(chunk, "item_id", None),
                summary_index=getattr(chunk, "summary_index", None),
            )

        elif chunk_type == "response.reasoning_summary_text.done":
            summary_text = getattr(chunk, "text", "")
            return StreamChunk(
                type="reasoning_summary_done",
                content=f"\n📋 [Reasoning Summary Complete]\n",
                reasoning_summary_text=summary_text,
                item_id=getattr(chunk, "item_id", None),
                summary_index=getattr(chunk, "summary_index", None),
            )

        # Provider tool events
        elif chunk_type == "response.web_search_call.in_progress":
            return StreamChunk(type="content", content=f"\n🔍 [Provider Tool: Web Search] Starting search...")
        elif chunk_type == "response.web_search_call.searching":
            return StreamChunk(type="content", content=f"\n🔍 [Provider Tool: Web Search] Searching...")
        elif chunk_type == "response.web_search_call.completed":
            return StreamChunk(type="content", content=f"\n✅ [Provider Tool: Web Search] Search completed")

        elif chunk_type == "response.code_interpreter_call.in_progress":
            return StreamChunk(type="content", content=f"\n💻 [Provider Tool: Code Interpreter] Starting execution...")
        elif chunk_type == "response.code_interpreter_call.executing":
            return StreamChunk(type="content", content=f"\n💻 [Provider Tool: Code Interpreter] Executing...")
        elif chunk_type == "response.code_interpreter_call.completed":
            return StreamChunk(type="content", content=f"\n✅ [Provider Tool: Code Interpreter] Execution completed")

        # MCP events
        elif chunk_type == "response.mcp_list_tools.started":
            return StreamChunk(type="content", content="\n🔧 [MCP] Listing available tools...")
        elif chunk_type == "response.mcp_list_tools.completed":
            return StreamChunk(type="content", content="\n✅ [MCP] Tool listing completed")
        elif chunk_type == "response.mcp_list_tools.failed":
            return StreamChunk(type="content", content="\n❌ [MCP] Tool listing failed")

        elif chunk_type == "response.mcp_call.started":
            tool_name = getattr(chunk, "tool_name", "unknown")
            return StreamChunk(type="content", content=f"\n🔧 [MCP] Calling tool '{tool_name}'...")
        elif chunk_type == "response.mcp_call.in_progress":
            return StreamChunk(type="content", content="\n⏳ [MCP] Tool execution in progress...")
        elif chunk_type == "response.mcp_call.completed":
            tool_name = getattr(chunk, "tool_name", "unknown")
            return StreamChunk(type="content", content=f"\n✅ [MCP] Tool '{tool_name}' completed")
        elif chunk_type == "response.mcp_call.failed":
            tool_name = getattr(chunk, "tool_name", "unknown")
            error_msg = getattr(chunk, "error", "unknown error")
            return StreamChunk(type="content", content=f"\n❌ [MCP] Tool '{tool_name}' failed: {error_msg}")

        elif chunk_type == "response.completed":
            if hasattr(chunk, "response"):
                response_dict = self._convert_to_dict(chunk.response)
                return StreamChunk(type="complete_response", response=response_dict)
            else:
                return StreamChunk(type="done")

        # Default case
        return StreamChunk(type="content", content="")

    async def stream_with_tools(
        self, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]], **kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """Stream response using OpenAI Response API with MCP integration."""
        try:
            import openai

            client = openai.AsyncOpenAI(api_key=self.api_key)

            # Setup stdio MCP tools if not already initialized
            if self._stdio_servers and not self._mcp_initialized:
                try:
                    await self._setup_mcp_tools()
                except Exception as e:
                    logger.warning(f"MCP setup failed, continuing without MCP: {e}")

            # Setup streamable HTTP MCP tools (treated like stdio) if configured
            if self._streamable_http_servers and not self._streamable_http_initialized:
                try:
                    await self._setup_streamable_http_mcp_tools()
                except Exception as e:
                    logger.warning(f"Streamable HTTP MCP setup failed, continuing without it: {e}")

            # Choose streaming mode based on stdio MCP availability
            if self.functions:
                # Mode A: With stdio MCP tools - use iterative execution loop
                logger.debug("Using stdio MCP execution mode")
                async for chunk in self._stream_with_stdio_mcp_execution(client, messages, tools, **kwargs):
                    yield chunk
            else:
                # Mode B: Without stdio MCP tools - single pass with HTTP MCP
                logger.debug("Using HTTP-only MCP mode")
                async for chunk in self._stream_without_stdio_mcp(client, messages, tools, **kwargs):
                    yield chunk

        except Exception as e:
            # Enhanced error handling for MCP-related errors
            if isinstance(e, (MCPConnectionError, MCPTimeoutError, MCPServerError, MCPError)):
                self._log_mcp_error(e, "streaming")
                yield StreamChunk(
                    type="content",
                    content=f"\n⚠️ MCP error: {str(e)}; continuing without MCP tools\n"
                )
            else:
                logger.error(f"Streaming error: {e}")
                yield StreamChunk(type="error", error=str(e))

    def get_provider_name(self) -> str:
        """Get the provider name."""
        return "OpenAI"

    def get_supported_builtin_tools(self) -> List[str]:
        """Get list of builtin tools supported by OpenAI."""
        return ["web_search", "code_interpreter"]

    def extract_tool_name(self, tool_call: Dict[str, Any]) -> str:
        """Extract tool name from OpenAI format (handles both Chat Completions and Responses API)."""
        # Check if it's Chat Completions format
        if "function" in tool_call:
            return tool_call.get("function", {}).get("name", "unknown")
        # Otherwise assume Responses API format
        return tool_call.get("name", "unknown")

    def extract_tool_arguments(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """Extract tool arguments from OpenAI format (handles both Chat Completions and Responses API)."""
        # Check if it's Chat Completions format
        if "function" in tool_call:
            return tool_call.get("function", {}).get("arguments", {})
        # Otherwise assume Responses API format
        arguments = tool_call.get("arguments", {})
        if isinstance(arguments, str):
            try:
                import json

                return json.loads(arguments)
            except:
                return {}
        return arguments

    def extract_tool_call_id(self, tool_call: Dict[str, Any]) -> str:
        """Extract tool call ID from OpenAI format (handles both Chat Completions and Responses API)."""
        # For Responses API, use call_id (for tool results), for Chat Completions use id
        return tool_call.get("call_id") or tool_call.get("id") or ""

    def create_tool_result_message(
        self, tool_call: Dict[str, Any], result_content: str
    ) -> Dict[str, Any]:
        """Create tool result message for OpenAI Responses API format."""
        tool_call_id = self.extract_tool_call_id(tool_call)
        # Use Responses API format directly - no conversion needed
        return {
            "type": "function_call_output",
            "call_id": tool_call_id,
            "output": result_content,
        }

    def extract_tool_result_content(self, tool_result_message: Dict[str, Any]) -> str:
        """Extract content from OpenAI Responses API tool result message."""
        return tool_result_message.get("output", "")

    def _convert_to_dict(self, obj) -> Dict[str, Any]:
        """Convert any object to dictionary with multiple fallback methods."""
        try:
            if hasattr(obj, "model_dump"):
                return obj.model_dump()
            elif hasattr(obj, "dict"):
                return obj.dict()
            else:
                return dict(obj)
        except:
            # Final fallback: extract key attributes manually
            return {
                key: getattr(obj, key, None)
                for key in dir(obj)
                if not key.startswith("_") and not callable(getattr(obj, key, None))
            }

    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)."""
        return len(text) // 4

    def calculate_cost(
        self, input_tokens: int, output_tokens: int, model: str
    ) -> float:
        """Calculate cost for OpenAI token usage (2024-2025 pricing)."""
        model_lower = model.lower()

        if "gpt-4" in model_lower:
            if "4o-mini" in model_lower:
                input_cost = input_tokens * 0.00015 / 1000
                output_cost = output_tokens * 0.0006 / 1000
            elif "4o" in model_lower:
                input_cost = input_tokens * 0.005 / 1000
                output_cost = output_tokens * 0.020 / 1000
            else:
                input_cost = input_tokens * 0.03 / 1000
                output_cost = output_tokens * 0.06 / 1000
        elif "gpt-3.5" in model_lower:
            input_cost = input_tokens * 0.0005 / 1000
            output_cost = output_tokens * 0.0015 / 1000
        else:
            input_cost = input_tokens * 0.0005 / 1000
            output_cost = output_tokens * 0.0015 / 1000

        return input_cost + output_cost

    async def cleanup_mcp(self) -> None:
        """Cleanup MCP connections."""
        if self._mcp_client:
            try:
                await self._mcp_client.disconnect()
                logger.info("MCP client disconnected successfully")
            except (
                MCPConnectionError,
                MCPTimeoutError,
                MCPServerError,
                MCPError,
                Exception,
            ) as e:
                self._log_mcp_error(e, "disconnect")
            finally:
                self._mcp_client = None
                self._mcp_initialized = False

        if self._streamable_mcp_client:
            try:
                await self._streamable_mcp_client.disconnect()
                logger.info("Streamable HTTP MCP client disconnected successfully")
            except (
                MCPConnectionError,
                MCPTimeoutError,
                MCPServerError,
                MCPError,
                Exception,
            ) as e:
                self._log_mcp_error(e, "disconnect")
            finally:
                self._streamable_mcp_client = None
                self._streamable_http_initialized = False

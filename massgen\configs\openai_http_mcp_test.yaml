# MassGen Configuration: OpenAI Response API with HTTP MCP Integration (mirrors Gemini streamable HTTP test)
# Prerequisites:
# 1. Start the test MCP server: python massgen/tests/test_http_mcp_server.py
# 2. Verify server is running at http://localhost:8000/mcp
# 3. Run: uv run python -m massgen.cli --config configs/openai_http_mcp_test.yaml "Run a complete system check - events, birthdays, random data, and server status"

agents:
  - id: "openai_http_mcp_test"
    backend:
      type: "openai"
      model: "gpt-5-nano"
      mcp_servers:
        - name: "http_test_server"
          type: "http" 
          url: "https://revolutionary-panther.fastmcp.app/mcp"
    system_message: |
      You are testing the MCP integration with HTTP transport using the "http_test_server".

      The HTTP/Streamable HTTP transport enables multiple client connections and supports server-to-client streaming.

      The following MCP tools from the server are available and will be auto-called when needed:
      - get_events(day: str): Retrieves calendar events for a specific day (e.g., "wednesday", "friday").
      - get_birthdays(): Retrieves a list of upcoming birthdays for the current week.
      - random_data(count: int = 3): Generates a specified number of random test data items.
      - server_status(): Performs a server health check and returns the current status and time.

      Do not output function-call syntax. Simply perform the tasks and present clear, concise results.

      Tasks to verify:
      - Calendar events: Check events for "Wednesday" and "Friday".
      - Birthdays: Check for upcoming birthdays.
      - Random data generation: Request a few random data items.
      - Server status: Verify the server is healthy and reports the time.

ui:
  display_type: "rich_terminal"
  logging_enabled: true

from __future__ import annotations

"""
Response API backend implementation.
Standalone implementation optimized for the standard Response API format (originated by OpenAI).
"""

import os
import logging
import time
import json
import asyncio
from typing import Dict, List, Any, AsyncGenerator, Optional, Union, Callable
from .base import LLMBackend, StreamChunk

logger = logging.getLogger(__name__)


# MCP integration imports
try:
    from ..mcp_tools import MultiMCPClient, MCPError, MCPConnectionError
    from ..mcp_tools.config_validator import MCPConfigValidator
    from ..mcp_tools.circuit_breaker import MCPCircuitBreaker
    from ..mcp_tools.exceptions import (
        MCPConfigurationError,
        MCPValidationError,
        MCPTimeoutError,
        MCPServerError,
    )
    from ..mcp_tools.security import validate_url
except ImportError:  # MCP not installed or import failed within mcp_tools
    MultiMCPClient = None  # type: ignore[assignment]
    MCPError = ImportError  # type: ignore[assignment]
    MCPConnectionError = ImportError  # type: ignore[assignment]
    MCPConfigValidator = None  # type: ignore[assignment]
    MCPCircuitBreaker = None  # type: ignore[assignment]
    MCPConfigurationError = ImportError  # type: ignore[assignment]
    MCPValidationError = ImportError  # type: ignore[assignment]
    MCPTimeoutError = ImportError  # type: ignore[assignment]
    MCPServerError = ImportError  # type: ignore[assignment]
    validate_url = None


class Function:
    """Function wrapper for MCP tools that can be called by OpenAI Response API."""

    def __init__(self, name: str, description: str, parameters: Dict[str, Any], entrypoint: Callable[[str], Any]) -> None:
        self.name = name
        self.description = description
        self.parameters = parameters
        self.entrypoint = entrypoint
    
    async def call(self, input_str: str) -> Any:
        """Call the function with input string."""
        return await self.entrypoint(input_str)
        
    
    def to_openai_format(self) -> Dict[str, Any]:
        """Convert function to OpenAI tool format."""
        return {
            "type": "function",
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
        }


class ResponseBackend(LLMBackend):

    """Backend using the standard Response API format."""

    def __init__(self, api_key: Optional[str] = None, **kwargs) -> None:
        super().__init__(api_key, **kwargs)
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")

        # MCP integration
        self.mcp_servers = kwargs.pop("mcp_servers", [])
        self.allowed_tools = kwargs.pop("allowed_tools", None)
        self.exclude_tools = kwargs.pop("exclude_tools", None)
        self._mcp_client: Optional[MultiMCPClient] = None
        self._mcp_initialized = False

        # MCP tool execution monitoring
        self._mcp_tool_calls_count = 0
        self._mcp_tool_failures = 0

        # Circuit breaker for HTTP MCP servers
        self._circuit_breaker = MCPCircuitBreaker() if MCPCircuitBreaker else None
        # Circuit breaker for stdio MCP servers
        self._stdio_circuit_breaker = MCPCircuitBreaker() if MCPCircuitBreaker else None

        # Separation containers for different transport types
        self._stdio_servers: List[Dict[str, Any]] = []
        self._http_servers: List[Dict[str, Any]] = []

        # Function registry for stdio MCP tools
        self.functions: Dict[str, Function] = {}
        
        # Thread safety for counters
        self._stats_lock = asyncio.Lock()

        # Separate MCP servers by transport type if any are configured
        if self.mcp_servers:
            self._separate_mcp_servers_by_transport_type()

    def _normalize_mcp_servers(self) -> List[Dict[str, Any]]:
        """Validate and normalize mcp_servers into a list of dicts."""
        servers = self.mcp_servers
        if not servers: return []
        if isinstance(servers, dict): return [servers]
        if not isinstance(servers, list):
            raise ValueError(
                f"mcp_servers must be a list or dict, got {type(servers).__name__}"
            )
        normalized: List[Dict[str, Any]] = []
        for idx, entry in enumerate(servers):
            if not isinstance(entry, dict):            
                raise ValueError(
                    f"MCP server configuration at index {idx} must be a dictionary, got {type(entry).__name__}"
                )        
            normalized.append(entry)
        return normalized

    def _separate_mcp_servers_by_transport_type(self) -> None:
        """Separate MCP servers into stdio and HTTP transport types."""
        validated_servers = self._normalize_mcp_servers()

        for server in validated_servers:
            # Remove stdio as default - require explicit type declaration
            transport_type = server.get("type")
            server_name = server.get("name", "unnamed")

            if not transport_type:
                logger.warning(
                    f"MCP server '{server_name}' missing required 'type' field. "
                    f"Supported types: 'stdio', 'streamable-http', 'http'. Skipping server."
                )
                continue

            if transport_type == "stdio":
                self._stdio_servers.append(server)
            elif transport_type in ("streamable-http", "http"):
                self._http_servers.append(server)
            else:
                # No longer default unknown types to stdio - skip with warning
                logger.warning(
                    f"Unknown MCP transport type '{transport_type}' for server '{server_name}'. "
                    f"Supported types: 'stdio', 'streamable-http', 'http'. Skipping server."
                )

    async def _setup_mcp_tools(self) -> None:
        """Initialize MCP client for stdio servers only."""
        if not self._stdio_servers or self._mcp_initialized: return

        if MultiMCPClient is None:
            reason = "MCP import failed - MultiMCPClient not available"
            logger.warning(
                "MCP support import failed (%s). stdio mcp_servers were provided; falling back to workflow tools without MCP. Ensure the 'mcp' package is installed and compatible with this codebase.",
                reason,
            )
            # Clear stdio servers to prevent further attempts
            self._stdio_servers = []
            return

        try:
            # Extract tool filtering parameters (use instance variables as fallback)
            allowed_tools = self.allowed_tools
            exclude_tools = self.exclude_tools

            # Validate MCP configuration before initialization
            if MCPConfigValidator is not None:
                try:
                    backend_config = {
                        "mcp_servers": self._stdio_servers,
                        "allowed_tools": self.allowed_tools,
                        "exclude_tools": self.exclude_tools,
                    }

                    # Use the comprehensive validator class for enhanced validation
                    validator = MCPConfigValidator()
                    validated_config = validator.validate_backend_mcp_config(backend_config)

                    self._stdio_servers = validated_config.get("mcp_servers", self._stdio_servers)

                    # Extract validated tool filtering parameters
                    allowed_tools = validated_config.get("allowed_tools", self.allowed_tools)
                    exclude_tools = validated_config.get("exclude_tools", self.exclude_tools)

                    logger.debug(
                        f"MCP configuration validation passed for {len(self._stdio_servers)} stdio servers"
                    )

                    # Log validated server names for debugging
                    if logger.isEnabledFor(logging.DEBUG):
                        server_names = [
                            server.get("name", "unnamed") for server in self._stdio_servers
                        ]
                        logger.debug(f"Validated MCP stdio servers: {', '.join(server_names)}")


                except MCPConfigurationError as e:
                    logger.error(f"MCP configuration validation failed: {e.original_message}")
                    self._mcp_client = None  # Clear client state for consistency
                    raise RuntimeError(
                        f"Invalid MCP configuration: {e.original_message}"
                    ) from e
                except MCPValidationError as e:
                    logger.error(f"MCP validation failed: {e.original_message}")
                    self._mcp_client = None  # Clear client state for consistency
                    raise RuntimeError(f"MCP validation error: {e.original_message}") from e
                except Exception as e:
                    if isinstance(e, (ImportError, AttributeError)):
                        logger.debug(f"MCP validation not available: {e}")
                        # Don't clear client for import errors - validation just unavailable
                    else:
                        logger.warning(f"MCP validation error: {e}")
                        self._mcp_client = None  # Clear client state for consistency
                        raise RuntimeError(
                            f"MCP configuration validation failed: {e}"
                        ) from e
            else:
                logger.debug("MCP validation not available, proceeding without validation")

            logger.info(
                f"Setting up MCP sessions with {len(self._stdio_servers)} stdio servers"
            )

            # Log tool filtering if configured
            if allowed_tools:
                logger.info(f"MCP tool filtering - allowed tools: {allowed_tools}")
            if exclude_tools:
                logger.info(f"MCP tool filtering - excluding: {exclude_tools}")

            # Create MCP client connection with retry logic and circuit breaker
            max_mcp_retries = 3
            mcp_connected = False

            for retry_count in range(1, max_mcp_retries + 1):
                try:
                    if retry_count > 1:
                        logger.info(f"MCP connection retry {retry_count}/{max_mcp_retries}")
                        await asyncio.sleep(0.5 * retry_count)  # Progressive backoff
                    
                    # Check circuit breaker for stdio servers
                    filtered_stdio_servers = []
                    if self._stdio_circuit_breaker:
                        for server in self._stdio_servers:
                            server_name = server.get("name", "unnamed")
                            if not self._stdio_circuit_breaker.should_skip_server(server_name):
                                filtered_stdio_servers.append(server)
                            else:
                                logger.debug(f"Skipping stdio server {server_name} due to circuit breaker")
                    else:
                        filtered_stdio_servers = self._stdio_servers
                    
                    if not filtered_stdio_servers:
                        logger.warning("All stdio servers are circuit breaker blocked")
                        break
                    
                    self._mcp_client = await MultiMCPClient.create_and_connect(
                        filtered_stdio_servers,
                        timeout_seconds=30,
                        allowed_tools=allowed_tools,
                        exclude_tools=exclude_tools,
                    )
                    
                    # Record success for circuit breaker
                    if self._stdio_circuit_breaker:
                        for server in filtered_stdio_servers:
                            server_name = server.get("name", "unnamed")
                            self._stdio_circuit_breaker.record_success(server_name)
                    
                    mcp_connected = True
                    logger.info(f"MCP connection successful on attempt {retry_count}")
                    break
                    
                except (MCPConnectionError, MCPTimeoutError, MCPServerError, MCPError) as e:
                    # Record failure for circuit breaker
                    if self._stdio_circuit_breaker:
                        for server in self._stdio_servers:
                            server_name = server.get("name", "unnamed")
                            self._stdio_circuit_breaker.record_failure(server_name)
                    
                    if retry_count >= max_mcp_retries:
                        logger.warning(f"MCP connection failed after {max_mcp_retries} attempts: {e}")
                        self._mcp_client = None
                        return  # Graceful fallback - don't raise exception
                    else:
                        logger.warning(f"MCP connection attempt {retry_count} failed: {e}")
            
            if not mcp_connected:
                logger.info("Falling back to workflow tools after MCP connection failures")
                return

            # Register tools from the client as Functions following user's approach
            for tool_name, tool in self._mcp_client.tools.items():
                try:
                    # Fix closure bug by using default parameter to capture tool_name
                    def create_tool_entrypoint(captured_tool_name: str = tool_name):
                        async def tool_entrypoint(input_str: str) -> Any:
                            return await self._mcp_client.call_tool(
                                captured_tool_name, json.loads(input_str)
                            )

                        return tool_entrypoint

                    # Create the entrypoint with captured tool name
                    entrypoint = create_tool_entrypoint()

                    # Create a Function for the tool
                    f = Function(
                        name=tool_name,
                        description=tool.description,
                        parameters=tool.inputSchema,
                        entrypoint=entrypoint,
                    )

                    # Register the Function
                    self.functions[f.name] = f
                    logger.debug(f"Function: {f.name} registered")
                except Exception as e:
                    logger.error(f"Failed to register tool {tool_name}: {e}")

            self._mcp_initialized = True
            logger.info(
                f"Successfully initialized MCP stdio sessions with {len(self._mcp_client.tools)} tools converted to functions"
            )

        except Exception as e:
            # Enhanced error handling for different MCP error types
            if isinstance(e, RuntimeError) and "MCP configuration" in str(e):
                raise
            elif isinstance(e, MCPConnectionError):
                logger.error(f"MCP connection failed during setup: {e}")
                self._mcp_client = None
                raise RuntimeError(f"Failed to establish MCP connections: {e}") from e
            elif isinstance(e, MCPTimeoutError):
                logger.error(
                    f"MCP connection timed out during setup: {e}"
                )
                self._mcp_client = None
                raise RuntimeError(f"MCP connection timeout: {e}") from e
            elif isinstance(e, MCPServerError):
                logger.error(f"MCP server error during setup: {e}")
                self._mcp_client = None
                raise RuntimeError(f"MCP server error: {e}") from e
            elif isinstance(e, MCPError):
                logger.warning(f"MCP error during setup: {e}")
                self._mcp_client = None
                return
            elif "MCP" in str(type(e).__name__):
                logger.warning(f"Unknown MCP-specific error during setup: {e}")
                self._mcp_client = None
                return
            else:
                logger.warning(f"Failed to setup MCP sessions: {e}")
                self._mcp_client = None

    def _convert_stdio_servers_to_openai_format(self) -> List[Dict[str, Any]]:
        """Convert stdio MCP tools to OpenAI function declarations."""
        if not self.functions:
            return []

        converted_tools = []
        for function in self.functions.values():
            converted_tools.append(function.to_openai_format())

        logger.debug(
            f"Converted {len(converted_tools)} stdio MCP tools to OpenAI format"
        )
        return converted_tools

    def _convert_http_servers_to_openai_format(self) -> List[Dict[str, Any]]:
        """Convert HTTP MCP servers to OpenAI native MCP format."""
        converted_servers = []

        for server in self._http_servers:    
            server_name = server.get("name", "unnamed")

            # Apply circuit breaker - skip if server is currently tripped            
            if self._circuit_breaker and self._circuit_breaker.should_skip_server(server_name):
                logger.debug(f"Skipping HTTP MCP server {server_name} due to circuit breaker")
                continue

            server_url = server.get("url")
            if not server_url:
                logger.warning(
                    f"HTTP MCP server {server_name} missing URL, skipping"
                )
                continue

            # Validate URL if validate_url is available
            if validate_url is not None:
                try:
                    # Allow localhost/private IPs to support dev/test environments
                    validate_url(
                        server_url, 
                        allow_localhost=True, 
                        allow_private_ips=True
                    )
                except ValueError as e:
                    logger.warning(f"Invalid URL for HTTP MCP server {server_name}: {e}")
                    if self._circuit_breaker:
                        self._circuit_breaker.record_failure(server_name)
                    continue

            # Convert to OpenAI native MCP format
            openai_server = {
                "type": "mcp", 
                "server_label": server_name, 
                "server_url": server_url, 
                "require_approval": "never", 
            }

            # Add allowed_tools if present
            if "allowed_tools" in server:
                openai_server["allowed_tools"] = server["allowed_tools"]

            converted_servers.append(openai_server)

        return converted_servers

    @staticmethod
    def _get_mcp_error_info(error: Exception) -> tuple[str, str, str]:
        """Get standardized MCP error information.

        Returns:
            tuple: (log_type, user_message, error_category)
        """
        error_mappings = {
            MCPConnectionError: ("connection error", "MCP connection failed", "connection"),
            MCPTimeoutError: ("timeout error", "MCP session timeout", "timeout"),
            MCPServerError: ("server error", "MCP server error", "server"),
            MCPError: ("MCP error", "MCP error", "general"),
        }

        return error_mappings.get(
            type(error), ("unexpected error", "MCP communication error", "unknown")
        )

    def _log_mcp_error(self, error: Exception, context: str) -> None:
        """Log MCP errors with specific error type messaging."""
        log_type, _, _ = self._get_mcp_error_info(error)

        logger.warning(f"MCP {log_type} during {context}: {error}")   

    async def _handle_mcp_error_and_fallback(
        self,
        error: Exception,
        api_params: Dict[str, Any],
        provider_tools: List[Dict[str, Any]],
        _stream_responses,
    ) -> AsyncGenerator[StreamChunk, None]:
        """Handle MCP errors with specific messaging and fallback to non-MCP tools."""
        # Thread safety for counter updates
        async with self._stats_lock:
            self._mcp_tool_failures += 1

        log_type, user_message, _ = self._get_mcp_error_info(error)

        # Log with specific error type
        logger.warning(
            f"MCP tool call #{self._mcp_tool_calls_count} failed - {log_type}: {error}"
        )

        # Yield user-friendly error message
        yield StreamChunk(
            type="content",
            content=f"\n⚠️  {user_message} ({error}); continuing without MCP tools\n",
        )

        # Build non-MCP configuration and stream fallback
        fallback_params = dict(api_params)

        # Remove any HTTP MCP tools from the tools list
        if "tools" in fallback_params:    
            non_mcp_tools = [
                tool
                for tool in fallback_params["tools"]
                if tool.get("type") != "mcp"
            ] # noqa
            fallback_params["tools"] = non_mcp_tools

        # Add back provider tools if they were present
        if provider_tools:
            if "tools" not in fallback_params: fallback_params["tools"] = []
            fallback_params["tools"].extend(provider_tools) 

        async for chunk in _stream_responses(fallback_params): yield chunk

    def convert_tools_to_response_api_format(
        self, tools: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert tools from Chat Completions format to Response API format if needed.

        Chat Completions format: {"type": "function", "function": {"name": ..., "description": ..., "parameters": ...}}
        Response API format: {"type": "function", "name": ..., "description": ..., "parameters": ...}
        """
        if not tools: return tools

        converted_tools = []
        for tool in tools:
            if tool.get("type") == "function" and "function" in tool:
                # Chat Completions format - convert to Response API format
                func = tool["function"]
                converted_tools.append(
                    {
                        "type": "function",
                        "name": func["name"],
                        "description": func["description"],
                        "parameters": func.get("parameters", {}),
                    }
                )
            else:
                # Already in Response API format or non-function tool
                converted_tools.append(tool)

        return converted_tools

    def convert_messages_to_response_api_format(
        self, messages: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert messages from Chat Completions format to Response API format.

        Chat Completions tool message: {"role": "tool", "tool_call_id": "...", "content": "..."}
        Response API tool message: {"type": "function_call_output", "call_id": "...", "output": "..."}

        Note: 'status' from messages without 'role' will be removed to match the format expected by the backend.
        """
        # First pass: remove 'status' from messages without 'role'
        cleaned_messages = []
        for message in messages:
            if "status" in message and "role" not in message:
                # Create a copy without 'status'
                cleaned_message = {
                    k: v for k, v in message.items() if k != "status"
                }
                cleaned_messages.append(cleaned_message)
            else:
                cleaned_messages.append(message)

        converted_messages = []

        for message in cleaned_messages:

            # Check if 'status' field exists
            if message.get("role") == "tool":
                # Convert Chat Completions tool message to Response API format
                converted_message = {
                    "type": "function_call_output",
                    "call_id": message.get("tool_call_id"),
                    "output": message.get("content", ""),
                }
                converted_messages.append(converted_message)
            elif message.get("type") == "function_call_output":
                converted_messages.append(message)
            elif message.get("role") == "assistant" and "tool_calls" in message:
                # Assistant message with tool_calls in native Responses API format
                # Remove tool_calls when sending as input - only results should be returned
                cleaned_message = {k: v for k, v in message.items() if k != "tool_calls"}
                converted_messages.append(cleaned_message)
            else:
                # For other message types, just ensure they have an id
                filtered_message = message.copy()
                converted_messages.append(filtered_message)

        return converted_messages


    async def stream_with_tools(
        self, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]], **kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """Stream response using OpenAI Response API."""
        try:
            import openai 

            client = openai.AsyncOpenAI(api_key=self.api_key)

            # Merge constructor config with stream kwargs (stream kwargs take priority)
            all_params = {**self.config, **kwargs}

            # Extract provider tool settings
            enable_web_search = all_params.get("enable_web_search", False)
            enable_code_interpreter = all_params.get(
                "enable_code_interpreter", False
            )


            # Convert messages to Response API format (handles tool messages)
            converted_messages = self.convert_messages_to_response_api_format(messages)


            # Response API parameters (uses 'input', not 'messages')
            api_params = {"input": converted_messages, "stream": True}


            # Direct passthrough of all parameters except those handled separately
            excluded_params = {
                "enable_web_search",
                "enable_code_interpreter",
                "agent_id",
                "session_id",
                "type",  # Used for MCP server configuration, not for OpenAI API
                "mcp_servers",  # MCP-specific parameter
                "allowed_tools",  # Tool filtering parameter
                "exclude_tools",  # Tool filtering parameter
            }
            for key, value in all_params.items():
                if key not in excluded_params and value is not None:
                    # Handle OpenAI Response API parameter name differences
                    if key == "max_tokens":
                        api_params["max_output_tokens"] = value
                    else:
                        api_params[key] = value


            # Add framework tools (convert to Response API format)
            if tools:
                converted_tools = self.convert_tools_to_response_api_format(tools)
                api_params["tools"] = converted_tools


            # Add provider tools (web search, code interpreter) if enabled
            provider_tools = []
            if enable_web_search: provider_tools.append({"type": "web_search"})
            if enable_code_interpreter:
                provider_tools.append(
                    {"type": "code_interpreter", "container": {"type": "auto"}},
                )


            if provider_tools:
                if "tools" not in api_params: api_params["tools"] = []
                api_params["tools"].extend(provider_tools)


            # Handle MCP integration
            try:
                # Setup stdio MCP servers if not already initialized
                if self._stdio_servers and not self._mcp_initialized:
                    await self._setup_mcp_tools()

                # Add stdio MCP tools to OpenAI API parameters
                if self._mcp_client and self._mcp_initialized and self.functions:
                    stdio_tools = self._convert_stdio_servers_to_openai_format()
                    if stdio_tools:
                        if "tools" not in api_params: api_params["tools"] = []
                        api_params["tools"].extend(stdio_tools)
                        logger.info(f"✅ Added {len(stdio_tools)} stdio MCP tools to OpenAI Response API")

                # Add HTTP MCP servers to OpenAI native MCP format
                if self._http_servers:
                    http_mcp_tools = self._convert_http_servers_to_openai_format()
                    if http_mcp_tools:
                        if "tools" not in api_params: api_params["tools"] = []
                        api_params["tools"].extend(http_mcp_tools)
                        logger.debug(
                            f"Added {len(http_mcp_tools)} HTTP MCP servers to OpenAI tools"
                        )


                # DIAGNOSTIC: Log what tools are actually being sent to OpenAI
                if "tools" in api_params:
                    tools_summary = []
                    stdio_count = 0
                    http_count = 0
                    for tool in api_params["tools"]:
                        if tool.get("type") == "mcp":
                            # MCP-servers supported by API, not MCP tools
                            tools_summary.append(f"MCP:{tool.get('server_label', 'unknown')}n")
                            http_count += 1
                        elif tool.get("type") == "function" and tool.get("name") in self.functions:
                            tools_summary.append(f"stdio-MCP:{tool.get('name', 'unknown')}")  # MCP tool
                            stdio_count += 1
                        else:
                            tools_summary.append(
                                f"{tool.get('type', 'unknown')}:{tool.get('name', 'unknown')}n"
                            )
                    logger.info(f"🔧 DIAGNOSTIC: Sending tools to OpenAI Response API: {tools_summary}")
                    logger.info(f"🔧 DIAGNOSTIC: stdio MCP tools: {stdio_count}, HTTP MCP servers: {http_count}")
                else:
                    logger.warning("🔧 DIAGNOSTIC: No tools being sent to OpenAI Response API")
            except Exception as e:
                # Handle MCP setup errors gracefully

                async def _stream_fallback(params):
                    fallback_stream = await client.responses.create(**params)
                    async for chunk in fallback_stream:
                        yield chunk

                async for error_chunk in self._handle_mcp_error_and_fallback(
                    e, api_params, provider_tools, _stream_fallback
                ):
                    yield error_chunk
                return

            stream = await client.responses.create(**api_params)

            content = ""
            async for chunk in stream:
                # Handle Responses API streaming format
                if hasattr(chunk, "type"):
                    if (chunk.type == "response.output_text.delta" and hasattr(chunk, "delta")):
                        content += chunk.delta
                        yield StreamChunk(type="content", content=chunk.delta)
                    elif (chunk.type == "response.reasoning_text.delta" and hasattr(chunk, "delta")):
                        # Stream reasoning process as it develops
                        yield StreamChunk(
                            type="reasoning",
                            content=f"🧠 [Reasoning] {chunk.delta}",
                            reasoning_delta=chunk.delta,
                            item_id=getattr(chunk, "item_id", None),
                            content_index=getattr(chunk, "content_index", None),
                        )
                    elif chunk.type == "response.reasoning_text.done":
                        # Complete reasoning step finished
                        reasoning_text = getattr(chunk, "text", "")
                        yield StreamChunk(
                            type="reasoning_done",
                            content=f"\n🧠 [Reasoning Complete]\n",
                            reasoning_text=reasoning_text,
                            item_id=getattr(chunk, "item_id", None),
                            content_index=getattr(chunk, "content_index", None),
                        )
                    elif (chunk.type == "response.reasoning_summary_text.delta" and hasattr(chunk, "delta")):
                        # Stream reasoning summary as it develops
                        yield StreamChunk(
                            type="reasoning_summary",
                            content=chunk.delta,  # Raw delta content without prefix
                            reasoning_summary_delta=chunk.delta,
                            item_id=getattr(chunk, "item_id", None),
                            summary_index=getattr(chunk, "summary_index", None),
                        )
                    elif chunk.type == "response.reasoning_summary_text.done":
                        # Complete reasoning summary finished
                        summary_text = getattr(chunk, "text", "")
                        yield StreamChunk(
                            type="reasoning_summary_done",
                            content=f"\n📋 [Reasoning Summary Complete]\n",
                            reasoning_summary_text=summary_text,
                            item_id=getattr(chunk, "item_id", None),
                            summary_index=getattr(chunk, "summary_index", None),
                        )
                    elif chunk.type == "response.web_search_call.in_progress":
                        yield StreamChunk(
                            type="content",
                            content=f"\n🔍 [Provider Tool: Web Search] Starting search...",
                        )
                    elif chunk.type == "response.web_search_call.searching":
                        yield StreamChunk(
                            type="content",
                            content=f"\n🔍 [Provider Tool: Web Search] Searching...",
                        )
                    elif chunk.type == "response.web_search_call.completed":
                        yield StreamChunk(
                            type="content",
                            content=f"\n✅ [Provider Tool: Web Search] Search completed...",
                        )
                    elif chunk.type == "response.code_interpreter_call.in_progress":
                        yield StreamChunk(
                            type="content",
                            content=f"\n💻 [Provider Tool: Code Interpreter] Starting execution...",
                        )
                    elif chunk.type == "response.code_interpreter_call.executing":
                        yield StreamChunk(
                            type="content",
                            content=f"\n💻 [Provider Tool: Code Interpreter] Executing...",
                        )
                    elif chunk.type == "response.code_interpreter_call.completed":
                        yield StreamChunk(
                            type="content",
                            content=f"\n🎉 [Provider Tool: Code Interpreter] Execution completed...",
                        )
                    # MCP streaming event handlers
                    elif chunk.type == "response.mcp_list_tools.started":
                        yield StreamChunk(
                            type="content", content="\n🔧 [MCP] Listing available tools...",
                        )
                    elif chunk.type == "response.mcp_list_tools.completed":
                        yield StreamChunk(
                            type="content", content="\n✅ [MCP] Tool listing completed",
                        )
                    elif chunk.type == "response.mcp_list_tools.failed":
                        yield StreamChunk(type="content", content="\n❌ [MCP] Tool listing failed")
                    elif chunk.type == "response.mcp_call.started":
                        tool_name = getattr(chunk, "tool_name", "unknown")
                        yield StreamChunk(
                            type="content",
                            content=f"\n🔧 [MCP] Calling tool '{tool_name}'...",
                        )
                    elif chunk.type == "response.mcp_call.in_progress":
                        yield StreamChunk(
                            type="content",
                            content="\n⏳ [MCP] Tool execution in progress...",
                        )
                    elif chunk.type == "response.mcp_call.completed":
                        tool_name = getattr(chunk, "tool_name", "unknown")
                        result_snippet = ""
                        if hasattr(chunk, "result") and chunk.result:
                            # Show a brief snippet of the result
                            result_str = str(chunk.result)
                            if len(result_str) > 100:
                                result_snippet = f" → {result_str[:100]}..."
                            else:
                                result_snippet = f" → {result_str}"
                        yield StreamChunk(
                            type="content",
                            content=f"\n✅ [MCP] Tool '{tool_name}' completed{result_snippet}",
                        )
                    elif chunk.type == "response.mcp_call.failed":
                        tool_name = getattr(chunk, "tool_name", "unknown")
                        error_msg = getattr(chunk, "error", "unknown error")
                        yield StreamChunk(
                            type="content",
                            content=f"\n❌ [MCP] Tool '{tool_name}' failed: {error_msg}",
                        )
                    elif chunk.type == "response.output_item.done":
                        # Get search query or executed code details - show them right after completion
                        if hasattr(chunk, "item") and chunk.item:
                            if (
                                hasattr(chunk.item, "type")
                                and chunk.item.type == "web_search_call"
                            ):
                                if hasattr(chunk.item, "action") and ("query" in chunk.item.action):
                                    search_query = chunk.item.action["query"]
                                    if search_query:
                                        yield StreamChunk(
                                            type="content",
                                            content=f"\n🔍 [Search Query] '{search_query}'\n",
                                        )
                            elif (
                                hasattr(chunk.item, "type")
                                and chunk.item.type == "code_interpreter_call"
                            ):
                                if hasattr(chunk.item, "code") and chunk.item.code:
                                    # Format code as a proper code block - don't assume language
                                    yield StreamChunk(
                                        type="content",
                                        content=f"💻 [Code Executed]\n```\n{chunk.item.code}\n```\n",
                                    )

                                # Also show the execution output if available
                                if (
                                    hasattr(chunk.item, "outputs")
                                    and chunk.item.outputs
                                ):
                                    for output in chunk.item.outputs:
                                        output_text = None
                                        if hasattr(output, "text") and output.text:
                                            output_text = output.text
                                        elif hasattr(output, "content") and output.content:
                                            output_text = output.content
                                        elif hasattr(output, "data") and output.data:
                                            output_text = str(output.data)
                                        elif isinstance(output, str):
                                            output_text = output
                                        elif isinstance(output, dict):
                                            # Handle dict format outputs
                                            if "text" in output:
                                                output_text = output["text"]
                                            elif "content" in output:
                                                output_text = output["content"]
                                            elif "data" in output:
                                                output_text = str(output["data"])

                                        if output_text and output_text.strip():
                                            yield StreamChunk(
                                                type="content",
                                                content=f"📊 [Result] {output_text.strip()}\n",
                                            )
                    elif chunk.type == "response.completed":
                        # Extract and yield tool calls from the complete response
                        if hasattr(chunk, "response"):
                            response_dict = self._convert_to_dict(chunk.response)

                            # Handle builtin tool results from output array with simple content format
                            if (
                                isinstance(response_dict, dict)
                                and "output" in response_dict
                            ):
                                for item in response_dict["output"]:
                                    if item.get("type") == "code_interpreter_call":
                                        # Code execution result
                                        status = item.get("status", "unknown")
                                        code = item.get("code", "")
                                        outputs = item.get("outputs")
                                        content = f"\n🔧 Code Interpreter [{status.title()}]"
                                        if code:
                                            content += f": {code}"
                                        if outputs:
                                            content += f" → {outputs}"

                                        yield StreamChunk(
                                            type="content", content=content
                                        )
                                    elif item.get("type") == "web_search_call":
                                        # Web search result
                                        status = item.get("status", "unknown")
                                        # Query is in action.query, not directly in item
                                        query = item.get("action", {}).get(
                                            "query", ""
                                        )
                                        results = item.get("results")

                                        # Only show web search completion if query is present
                                        if query:
                                            content = f"\n🔧 Web Search [{status.title()}]: {query}"
                                            if results:
                                                content += f" → Found {len(results)} results"

                                            yield StreamChunk(
                                                type="tool", content=content
                                            )

                            # Yield the complete response for internal use
                            yield StreamChunk(
                                type="complete_response", response=response_dict
                            )
                        else:
                            # Fallback if no response object
                            complete_message = {
                                "role": "assistant",
                                "content": content.strip(),
                            }
                            yield StreamChunk(
                                type="complete_message",
                                complete_message=complete_message,
                            )

                        # Signal completion
                        yield StreamChunk(type="done")

        except Exception as e:
            yield StreamChunk(type="error", error=str(e))


    def get_provider_name(self) -> str:
        """Get the provider name."""
        return "OpenAI"


    def get_supported_builtin_tools(self) -> List[str]:
        """Get list of builtin tools supported by OpenAI."""
        return ["web_search", "code_interpreter"]


    def extract_tool_name(self, tool_call: Dict[str, Any]) -> str:
        """Extract tool name from OpenAI format (handles both Chat Completions and Responses API)."""
        # Check if it's Chat Completions format
        if "function" in tool_call:
            return tool_call.get("function", {}).get("name", "unknown")
        # Otherwise assume Responses API format
        return tool_call.get("name", "unknown")


    def extract_tool_arguments(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """Extract tool arguments from OpenAI format (handles both Chat Completions and Responses API)."""
        # Check if it's Chat Completions format
        if "function" in tool_call:
            return tool_call.get("function", {}).get("arguments", {})
        # Otherwise assume Responses API format
        arguments = tool_call.get("arguments", {})
        if isinstance(arguments, str):
            try:
                import json

                return json.loads(arguments)
            except (json.JSONDecodeError, ValueError, TypeError):
                return {}
        return arguments


    def extract_tool_call_id(self, tool_call: Dict[str, Any]) -> str:
        """Extract tool call ID from OpenAI format (handles both Chat Completions and Responses API)."""
        # For Responses API, use call_id (for tool results), for Chat Completions use id
        return tool_call.get("call_id", None) or tool_call.get("id", "") \
            or ""


    def create_tool_result_message(
        self, tool_call: Dict[str, Any], result_content: str
    ) -> Dict[str, Any]:
        """Create tool result message for OpenAI Responses API format."""
        tool_call_id = self.extract_tool_call_id(tool_call)
        # Use Responses API format directly - no conversion needed
        return {
            "type": "function_call_output",
            "call_id": tool_call_id,
            "output": result_content,
        }


    def extract_tool_result_content(self, tool_result_message: Dict[str, Any]) -> str:
        """Extract content from OpenAI Responses API tool result message."""
        return tool_result_message.get("output", "")


    def _convert_to_dict(self, obj) -> Dict[str, Any]:
        """Convert any object to dictionary with multiple fallback methods."""
        try:
            # Prefer model_dump over dict to support Pydantic 2.x
            if hasattr(obj, "model_dump"):
                return obj.model_dump()  # Pydantic 2.x
            elif hasattr(obj, "dict"):
                return obj.dict()  # Pydantic 1.x
            else:
                return dict(obj)
        except (AttributeError, TypeError, ValueError):
            # Final fallback: extract key attributes manually
            return {
                key: getattr(obj, key, None)
                for key in dir(obj)
                if not key.startswith("_")
                and not callable(getattr(obj, key, None))
            }


    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)."""
        return len(text) // 4


    def calculate_cost(
        self, input_tokens: int, output_tokens: int, model: str
    ) -> float:
        """Calculate cost for OpenAI token usage (2024-2025 pricing)."""
        model_lower = model.lower()

        if "gpt-4" in model_lower:
            if "4o-mini" in model_lower:
                input_cost = input_tokens * 0.00015 / 1000
                output_cost = output_tokens * 0.0006 / 1000
            elif "4o" in model_lower:  # gpt-4o, gpt-4o-mini, etc.
                input_cost = input_tokens * 0.005 / 1000
                output_cost = output_tokens * 0.020 / 1000
            else:  # Default to gpt-4
                input_cost = input_tokens * 0.03 / 1000
                output_cost = output_tokens * 0.06 / 1000
        elif "gpt-3.5" in model_lower:
            input_cost = input_tokens * 0.0005 / 1000
            output_cost = output_tokens * 0.0015 / 1000
        else:  # Default to gpt-3.5 if model is unknown
            input_cost = input_tokens * 0.0005 / 1000
            output_cost = output_tokens * 0.0015 / 1000

        return input_cost + output_cost


    async def cleanup_mcp(self) -> None:
        """Cleanup MCP connections."""
        if self._mcp_client:
            try:
                await self._mcp_client.disconnect()
                logger.info("MCP client disconnected successfully")
            except (
                MCPConnectionError,
                MCPTimeoutError,
                MCPServerError,
                MCPError,
                Exception,
            ) as e:
                self._log_mcp_error(e, "disconnect")
            finally:
                self._mcp_client = None
                self._mcp_initialized = False

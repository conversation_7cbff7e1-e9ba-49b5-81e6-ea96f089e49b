# MCP Tools Integration Plan for Response API Backend (response.py)

Goal: Support MCP tools via both stdio and HTTP transports in the OpenAI Response API backend, preserving current architecture and patterns.


## 1) Transport Separation and Initialization

- Normalize and separate configured MCP servers by transport type at construction:
  - stdio servers → self._stdio_servers
  - http/streamable-http servers → self._http_servers
- Keep a circuit breaker per transport to avoid flaky servers.
- Lazy-init stdio MCP client only if stdio servers exist and not initialized.


## 2) Stdio MCP: Register Tools as Custom Functions

- Connect to stdio MCP servers via MultiMCPClient.create_and_connect(...).
- Enumerate discovered tools and register each as a local Function wrapper:
  - name: tool_name
  - description: tool.description
  - parameters: tool.inputSchema
  - entrypoint: async def(input_str) → await mcp_client.call_tool(tool_name, json.loads(input_str))
- Maintain registry: self.functions: Dict[name, Function].
- Convert these to OpenAI Responses API tool declarations when building api_params.


## 3) HTTP MCP: Native Delegation to Provider

- Convert each HTTP/streamable-http server to OpenAI native MCP tool format:
  ```json
  {
    "type": "mcp",
    "server_label": "gitmcp",
    "server_url": "https://gitmcp.io/openai/tiktoken", 
    "allowed_tools": ["search_tiktoken_documentation", "fetch_tiktoken_documentation"],
    "require_approval": "never"
  }
  ```
- Add them directly to api_params["tools"].
- Let the Responses API handle HTTP MCP call execution and streaming events.
- Note: OpenAI Response API expects `model` parameter (e.g., "gpt-4.1") and `input` instead of `messages`.


## 4) Request Parameter Construction

- Create helper: _build_response_api_params(messages, tools, all_params)
  - **Required fields**:
    - `model`: Must be specified (e.g., "gpt-4.1")
    - `input`: converted messages (Response API format, NOT "messages")
    - `stream`: True for streaming responses
  - **Tool integration**:
    - Framework tools: converted from Chat Completions format if needed
    - Provider tools: web_search, code_interpreter based on flags
    - **Stdio MCP tools**: as custom function declarations
    - **HTTP MCP servers**: as native MCP tool objects with exact format:
      ```json
      {
        "type": "mcp",
        "server_label": "server_name",
        "server_url": "https://server.url",
        "allowed_tools": ["tool1", "tool2"],  // optional
        "require_approval": "never"
      }
      ```
  - **Parameter mapping**: max_tokens → max_output_tokens


## 5) Message Format Conversion Utilities

- convert_messages_to_response_api_format(messages):
  - Convert tool result messages from Chat Completions format → { type: "function_call_output", call_id, output }
  - Strip assistant.tool_calls from input messages
  - Clean stray status fields
- convert_tools_to_response_api_format(tools):
  - Transform Chat Completions function schema → Response API format when necessary


## 6) Streaming: Two Modes

A) With stdio tools available (functions registry non-empty):
- _stream_with_stdio_mcp_execution(client, messages, tools, **kwargs)
  - Iterative loop (max_iterations to avoid infinite loops):
    1) Build api_params and begin streaming
    2) Detect function calls in stream:
       - response.output_item.added with item.type == "function_call" → capture call_id, name
       - response.function_call_arguments.delta → accumulate JSON args string
       - response.output_item.done → finalize current function call and enqueue
    3) On response.completed:
       - If captured function calls exist, execute matching stdio tools locally:
         - Lookup by name in self.functions and await function.call(args_str)
         - Append { type: "function_call_output", call_id, output } to current_messages
         - Continue loop (next iteration) with updated messages to let model use tool outputs
       - Else emit done

B) Without stdio tools (HTTP-only or no MCP):
- _stream_without_stdio_mcp(client, messages, tools, **kwargs)
  - Single streaming pass
  - Provider MCP events (list_tools/call) are streamed and surfaced as content/status updates


## 7) Reliability and Error Handling

- Add retry with exponential backoff for responses.create(...) transport errors (reqwest/hyper/HTTP2):
  - max_retries = 3, backoff = 2^n up to 8s
- Validate OPENAI_API_KEY before client creation; increase timeout (e.g., 60s).
- Stdio MCP setup:
  - Validate config with MCPConfigValidator if present
  - Circuit breaker skip on repeated failures
  - Graceful fallback (no exception) if stdio MCP cannot initialize
- Tool execution exceptions:
  - For stdio calls: inject function_call_output with an Error: ... message payload so the model can recover


## 8) Minimal Public Surface and Compatibility

- Keep ResponseBackend.stream_with_tools as the only entry point.
- Internally decide mode (with/without stdio tools) based on registry state.
- Preserve existing StreamChunk categories and behavior for UI/agent orchestration.


## 9) Implementation Checklist

- Transport separation (existing): verify and keep
- Stdio setup (_setup_mcp_tools):
  - Connect, enumerate tools, populate self.functions
- HTTP conversion (_convert_http_servers_to_openai_format): keep + circuit breaker + URL validation
- Param builder (_build_response_api_params): include framework, provider, stdio, HTTP MCP
- Message/tools converters: ensure correctness for Responses API
- Streaming paths:
  - _stream_with_stdio_mcp_execution: implement function-call loop and local execution
  - _stream_without_stdio_mcp: provider-only flow
- Error handling: retries, timeouts, circuit breaker updates
- Cleanup: cleanup_mcp to disconnect on shutdown


## 10) Testing Plan

- Stdio tool:
  - Model triggers function_call → local execution → function_call_output appended → model continues
- HTTP MCP tool:
  - Model calls native MCP tool → provider emits mcp_call.* events → final response includes tool outputs
- Mixed setup:
  - Both stdio and HTTP servers configured → both tool types available simultaneously
- Failure cases:
  - Broken stdio server: fallback to non-stdio flow
  - HTTP MCP URL invalid: skip with warning; others still work
  - Transport blips: retries succeed or graceful error surfaced


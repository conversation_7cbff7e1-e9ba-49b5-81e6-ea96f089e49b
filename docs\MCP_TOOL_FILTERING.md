# MCP allowed tools Configuration

## Two Types of Tool Filtering

### 1. General Backend Level (MassGen)
**Location**: At backend level  
**Tool Names**: Prefixed format `mcp__server__tool`  
**Applies to**: All backends (<PERSON>, <PERSON>, etc.)

```yaml
agents:
  - backend:
      type: "gemini"
      allowed_tools: ["mcp__weather__get_forecast", "mcp__calculator__add"]
      exclude_tools: ["mcp__weather__dangerous_op", "mcp__system__delete"]
      mcp_servers:
        - name: "weather"
          type: "stdio"
        - name: "calculator" 
          type: "stdio"
```

### 2. OpenAI Backend Level (OpenAI Native)
**Location**: Inside MCP server config  
**Tool Names**: Raw format (no prefix)  
**Applies to**: Only OpenAI/Response backends with HTTP MCP servers

```yaml
agents:
  - backend:
      type: "openai"  # or "response"
      mcp_servers:
        - name: "weather_http"
          type: "http"
          url: "http://localhost:8080/mcp"
          allowed_tools: ["get_forecast", "get_current"]  # Raw names
        - name: "calculator_http"
          type: "http" 
          url: "http://localhost:8081/mcp"
          allowed_tools: ["add", "subtract"]  # Raw names
```

## Key Differences

| Aspect | General Backend | OpenAI Backend |
|--------|----------------|----------------|
| **Location** | `backend.allowed_tools` | `mcp_servers[].allowed_tools` |
| **Tool Format** | `mcp__server__tool` | `tool` |
| **Scope** | All MCP servers | Individual server |
| **Backend Support** | All backends | OpenAI/Response only |
| **exclude_tools** | ✅ Supported | ❌ Not supported |

## Example: Both Types Together

```yaml
agents:
  - backend:
      type: "openai"
      # General backend filtering (applies to stdio servers)
      allowed_tools: ["mcp__local_server__safe_tool"]
      exclude_tools: ["mcp__local_server__dangerous"]
      mcp_servers:
        # Stdio server (uses general backend filtering)
        - name: "local_server"
          type: "stdio"
          command: "python"
          args: ["-m", "my_server"]
        # HTTP server (uses OpenAI native filtering)  
        - name: "remote_server"
          type: "http"
          url: "http://api.example.com/mcp"
          allowed_tools: ["get_data", "process"]  # OpenAI format
```

## No Filtering (Default)
```yaml
agents:
  - backend:
      type: "gemini"
      # No allowed_tools = Accept ALL tools
      mcp_servers:
        - name: "server1"